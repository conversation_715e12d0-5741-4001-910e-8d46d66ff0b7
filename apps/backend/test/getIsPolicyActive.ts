import { getIsPolicyActive } from "../lambdas/fetchNewUsers/helpers";

interface PolicyDetails {
  id: string;
  properties: {
    policy_status: string;
    policy_type: string;
    policy_number: string;
  };
}

describe("getIsPolicyActive", () => {
  test("returns true if at least one policy has status 'Active'", () => {
    const policies: PolicyDetails[] = [
      {
        id: "12345",
        properties: {
          policy_status: "Cancelled",
          policy_type: "test",
          policy_number: "12345",
        },
      },
      {
        id: "54321",
        properties: {
          policy_status: "Active",
          policy_type: "test",
          policy_number: "54321",
        },
      },
    ];

    expect(getIsPolicyActive(policies)).toBe(true);
  });

  test("returns false if no policy has status 'Active'", () => {
    const policies: PolicyDetails[] = [
      {
        id: "12345",
        properties: {
          policy_status: "Cancelled",
          policy_type: "test",
          policy_number: "12345",
        },
      },
      {
        id: "54321",
        properties: {
          policy_status: "Cancelled",
          policy_type: "test",
          policy_number: "54321",
        },
      },
    ];

    expect(getIsPolicyActive(policies)).toBe(false);
  });

  test("returns false for an empty policies array", () => {
    const policies: PolicyDetails[] = [];

    expect(getIsPolicyActive(policies)).toBe(false);
  });
});
