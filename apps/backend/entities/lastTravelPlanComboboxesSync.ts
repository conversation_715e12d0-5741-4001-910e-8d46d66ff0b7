import { getEnvVariable } from '../helpers/getEnvVariable';
import { getItemDynamoDB, putItemDynamoDB } from '../lambdas/helpers';

const TABLE_NAME = getEnvVariable('PLATFORM_TABLE');

export interface LastTravelPlanComboboxesSync {
  pk: string;
  sk: string;
  date: string;
}

export const getLastTravelPlanComboboxesSync = async(): Promise<LastTravelPlanComboboxesSync | undefined> => {
  const response = await getItemDynamoDB<LastTravelPlanComboboxesSync>({
      TableName: TABLE_NAME,
      Key: {
        pk: "LAST_TRAVEL_PLAN_COMBOBOXES_SYNC",
        sk: "INFO",
      },
    });
  
    return response.Item;
};

export const updateLastTravelPlanComboboxesSync = async() => {
  await putItemDynamoDB({
      TableName: TABLE_NAME,
      Item: {
        pk: "LAST_TRAVEL_PLAN_COMBOBOXES_SYNC",
        sk: "INFO",
        date: new Date().toISOString(),
      },
    });
};