import { GetCommandInput, QueryCommandInput } from '@aws-sdk/lib-dynamodb';
import { getEnvVariable } from '../helpers/getEnvVariable';
import { batchWriteItemsDynamoDB, deleteItemDynamoDB, queryDynamoDB, getItemDynamoDB } from '../lambdas/helpers';
import { HubspotTravelPlanCombobox } from '../services/hubspot/travelPlanComboboxes';

const TABLE_NAME = getEnvVariable('PLATFORM_TABLE');

export interface TravelPlanCombobox {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  travelPlanQuestionId: string | undefined;
  travelPlanComboboxId: string;
  travelPlanComboboxOptionName: string;
  travelPlanComboboxOptionText: string;
  hubspotRecordId: string;
  hubspotCreatedDate: string;
}

export const updateTravelPlanComboboxesFromHubspot = async ({
  comboboxes,
}: {
  comboboxes: HubspotTravelPlanCombobox[];
}) => {
  console.log(`Updating ${comboboxes.length} travel plan comboboxes from Hubspot`);
  const BATCH_SIZE = 25; // DynamoDB batch write limit

  for (let i = 0; i < comboboxes.length; i += BATCH_SIZE) {
    const batch = comboboxes.slice(i, i + BATCH_SIZE);

    const writeRequests = batch.map((combobox) => {
      const comboboxItem: TravelPlanCombobox = {
        pk: `TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION#${combobox.travel_plan_multibox_cuid}`,
        sk: `TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION#${combobox.travel_plan_multibox_cuid}`,
        gsi1pk: "TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION",
        gsi1sk: `TRAVEL_PLAN_QUESTION#${combobox.travel_plan_question_cuid}#HUBSPOT_CREATED_DATE#${combobox.hs_createdate}#TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION#${combobox.travel_plan_multibox_cuid}`,
        travelPlanQuestionId: combobox.travel_plan_question_cuid,
        travelPlanComboboxId: combobox.travel_plan_multibox_cuid,
        travelPlanComboboxOptionName: combobox.combobox_option_name,
        travelPlanComboboxOptionText: combobox.option_text,
        hubspotRecordId: combobox.hs_object_id,
        hubspotCreatedDate: combobox.hs_createdate,
      };

      return {
        PutRequest: {
          Item: comboboxItem,
        },
      };
    });

    await batchWriteItemsDynamoDB({
      RequestItems: {
        [TABLE_NAME]: writeRequests,
      },
    });
  }
};

export const deleteTravelPlanCombobox = async (comboboxId: string) => {
  await deleteItemDynamoDB({
    TableName: TABLE_NAME,
    Key: {
      pk: `TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION#${comboboxId}`,
      sk: `TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION#${comboboxId}`,
    },
  });
};

export const batchDeleteTravelPlanComboboxes = async (comboboxIds: string[]) => {
  await Promise.all(
    comboboxIds.map((comboboxId) => deleteTravelPlanCombobox(comboboxId)),
  );
};  

export const queryTravelPlanComboboxesForQuestion = async (
  travelPlanQuestionId: string
) => {
  const params: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: 'GSI1',
    KeyConditionExpression: "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1skPrefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": "TRAVEL_PLAN_QUESTION_COMBOBOX_OPTION",
      ":gsi1skPrefix": `TRAVEL_PLAN_QUESTION#${travelPlanQuestionId}`
    },
  }

  const comboboxesResult = await queryDynamoDB<TravelPlanCombobox>(params);
  return comboboxesResult.Items ?? [];
};

export const getTravelPlanCombobox = async (comboboxId: string): Promise<TravelPlanCombobox | undefined> => {
  const travelPlanComboboxParams: GetCommandInput = {
    TableName: TABLE_NAME,
    Key: {
      pk: `TRAVEL_PLAN_QUESTION_COMBOBOX#${comboboxId}`,
      sk: `TRAVEL_PLAN_QUESTION_COMBOBOX#${comboboxId}`
    }
  }

  const travelPlanComboboxResult = await getItemDynamoDB<TravelPlanCombobox>(travelPlanComboboxParams);

  return travelPlanComboboxResult.Item;
};