import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  BatchWriteCommand,
  QueryCommandInput,
} from "@aws-sdk/lib-dynamodb";
import { putItemDynamoDB, queryDynamoDB } from "../lambdas/helpers";

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);

export interface PrizeSlot {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  rewardAmount: number;
  year: number;
  month: string;
  slotId: string;
  isClaimed: boolean;
  priority: number;
}

const chunkArray = <T>(arr: T[], size: number): T[][] => {
  return Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
    arr.slice(i * size, i * size + size),
  );
};

export const batchInsertPrizeSlots = async (
  slots: PrizeSlot[],
  tableName: string,
): Promise<void> => {
  // Chunk slots into groups of 25 (DynamoDB BatchWrite limit)
  const chunkedSlots = chunkArray(slots, 25);

  for (const chunk of chunkedSlots) {
    const batchWriteParams = {
      RequestItems: {
        [tableName]: chunk.map((slot) => ({
          PutRequest: {
            Item: slot,
          },
        })),
      },
    };

    await docClient.send(new BatchWriteCommand(batchWriteParams));
  }
};

export const queryNextPrizeSlot = async (
  prizeDrawId: string,
  tableName: string,
): Promise<PrizeSlot | null> => {
  const params: QueryCommandInput = {
    TableName: tableName,
    IndexName: "GSI1",
    KeyConditionExpression:
      "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1skPrefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": `PRIZE_DRAW#${prizeDrawId}`,
      ":gsi1skPrefix": "IS_CLAIMED#FALSE",
    },
    Limit: 1,
  };

  const { Items } = await queryDynamoDB<PrizeSlot>(params);
  return Items?.[0] || null;
};

export const updatePrizeSlotToClaimedTransaction = ({
  prizeSlot,
  prizeDrawPlayId,
  tableName,
}: {
  prizeSlot: PrizeSlot;
  prizeDrawPlayId: string;
  tableName: string;
}) => {
  const newGsi1sk = `IS_CLAIMED#TRUE#PRIORITY#${prizeSlot.priority}#SLOT_ID#${prizeSlot.slotId}`;

  return {
    TableName: tableName,
    Key: {
      pk: prizeSlot.pk,
      sk: prizeSlot.sk,
    },
    UpdateExpression:
      "SET gsi1sk = :newGsi1sk, isClaimed = :claimed, prizeDrawPlayId = :prizeDrawPlayId",
    ConditionExpression: "isClaimed = :notClaimed",
    ExpressionAttributeValues: {
      ":newGsi1sk": newGsi1sk,
      ":claimed": true,
      ":prizeDrawPlayId": prizeDrawPlayId,
      ":notClaimed": false,
    },
  };
};

export const addGuaranteedPrizeToSlots = async ({
  rewardAmount,
  prizeDrawId,
  slotId,
  year,
  month,
  tableName,
}: {
  rewardAmount: number;
  prizeDrawId: string;
  slotId: string;
  year: number;
  month: string;
  tableName: string;
}) => {
  await putItemDynamoDB({
    TableName: tableName,
    Item: {
      pk: `PRIZE_DRAW#${prizeDrawId}`,
      sk: `SLOT#${slotId}`,
      gsi1pk: `PRIZE_DRAW#${prizeDrawId}`,
      gsi1sk: `IS_CLAIMED#FALSE#PRIORITY#0#SLOT_ID#${slotId}`,
      rewardAmount,
      year,
      month,
      slotId,
      isClaimed: false,
      priority: 0,
    },
  });
};
