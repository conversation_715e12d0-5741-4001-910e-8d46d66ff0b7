import { v4 as uuidv4 } from "uuid";
import {
  getItemDynamoDB,
  putItemDynamoDB,
  queryDynamoDB,
} from "../lambdas/helpers";
import { getEnvVariable } from "../helpers/getEnvVariable";
import { QueryCommandInput } from "@aws-sdk/lib-dynamodb";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

interface Draw {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  prizeDrawPlayId: string;
  userId: string;
  timeStamp: string;
  prizeDrawId: string;
  pointsWon?: number;
}

export const putInitialPrizeDrawPlay = async (
  userId: string,
  prizeDrawId: string,
  drawPlayId: string,
) => {
  const timeStamp = new Date().toISOString();

  const drawItem: Draw = {
    pk: `USER#${userId}`,
    sk: `DRAWS#PRIZE_DRAW_PLAY_ID#${drawPlayId}`,
    gsi1pk: `USER#${userId}`,
    gsi1sk: `DRAWS#PRIZE_DRAW_ID#${prizeDrawId}#DATE#${timeStamp}`,
    timeStamp,
    userId,
    prizeDrawPlayId: drawPlayId,
    prizeDrawId,
  };

  return await putItemDynamoDB({
    TableName: TABLE_NAME,
    Item: drawItem,
  });
};

export const putPrizeDrawPlay = async (userId: string) => {
  const pointsWon = Math.floor(Math.random() * (1000000 - 10 + 1)) + 10;
  const timeStamp = new Date().toISOString();
  const drawPlayId = uuidv4();

  const drawItem: Draw = {
    pk: `USER#${userId}`,
    sk: `DRAWS#DATE#${timeStamp}`,
    gsi1pk: `USER#${userId}`,
    gsi1sk: `DRAWS#PRIZE_DRAW_ID#123#DATE#${timeStamp}`,
    pointsWon,
    timeStamp,
    userId,
    prizeDrawPlayId: drawPlayId,
    prizeDrawId: "temp",
  };

  const drawParams = {
    TableName: TABLE_NAME,
    Item: drawItem,
  };

  await putItemDynamoDB(drawParams);

  return {
    pointsWon,
    drawPlayId,
  };
};

export const queryPrizeDrawPlays = async (
  partitionKey: string,
  prizeDrawId: string,
): Promise<Draw[]> => {
  const drawParams: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: "GSI1",
    KeyConditionExpression:
      "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1skPrefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": partitionKey,
      ":gsi1skPrefix": `DRAWS#PRIZE_DRAW_ID#${prizeDrawId}#DATE#`,
    },
  };

  const drawResult = await queryDynamoDB<Draw>(drawParams);
  if (!drawResult) {
    console.error("Failed to find draws.");
    throw new Error("Failed to find draws.");
  }

  return drawResult.Items ?? [];
};

export const updatePrizeDrawPlayTransaction = ({
  userId,
  prizeDrawPlayId,
  pointsWon,
}: {
  userId: string;
  prizeDrawPlayId: string;
  pointsWon: number;
}) => ({
  TableName: TABLE_NAME,
  Key: {
    pk: `USER#${userId}`,
    sk: `DRAWS#PRIZE_DRAW_PLAY_ID#${prizeDrawPlayId}`,
  },
  UpdateExpression: "SET pointsWon = :pointsWon",
  ExpressionAttributeValues: {
    ":pointsWon": pointsWon,
  },
});

export const getPrizeDrawPlay = async (
  userId: string,
  prizeDrawPlayId: string,
) => {
  const drawResult = await getItemDynamoDB<Draw>({
    TableName: TABLE_NAME,
    Key: {
      pk: `USER#${userId}`,
      sk: `DRAWS#PRIZE_DRAW_PLAY_ID#${prizeDrawPlayId}`,
    },
  });

  if (!drawResult) {
    console.error("Failed to find draw.");
    return null;
  }

  return drawResult.Item;
};


