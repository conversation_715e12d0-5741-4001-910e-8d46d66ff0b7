import { v4 as uuidv4 } from "uuid";

import { HubspotUser } from "../lambdas/types";
import {
  deleteItemDynamoDB,
  getItemDynamoDB,
  putItemDynamoDB,
  queryDynamoDB,
  updateItemDynamoDB,
} from "../lambdas/helpers";
import { getEnvVariable } from "../helpers/getEnvVariable";
import { GetCommandInput, QueryCommandInput, UpdateCommandInput } from "@aws-sdk/lib-dynamodb";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

export interface User {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  baecMemberId: string | null;
  createDate: string;
  lastModifiedDate: string;
  userAddedToDynamoDB: string;
  isPolicyActive: boolean;
  hubspotRecordId: string;
}

export interface UserPlayingDrawLock {
  pk: string;
  sk: string;
  userId: string;
  drawId: string;
  expiresAt: number;
}

export const putUser = async (user: HubspotUser): Promise<User> => {
  const {
    firstname,
    lastname,
    iagl_baecmemberid,
    email,
    createdate,
    lastmodifieddate,
    isPolicyActive,
    hs_object_id,
    cuid,
  } = user;
  const userId = cuid || uuidv4(); // Only generate a CUID if we don't have one yet
  const userItem = {
    pk: `USER#${userId}`,
    sk: "INFO",
    gsi1pk: `USER`,
    gsi1sk: `IS_POLICY_ACTIVE#${isPolicyActive.toString().toUpperCase()}#USER#${userId}`,
    userId,
    firstName: firstname,
    lastName: lastname,
    email,
    baecMemberId: iagl_baecmemberid,
    createDate: createdate,
    lastModifiedDate: lastmodifieddate,
    userAddedToDynamoDB: new Date().toISOString(),
    isPolicyActive,
    hubspotRecordId: hs_object_id,
  };

  const userParams = {
    TableName: TABLE_NAME,
    Item: userItem,
  };

  await putItemDynamoDB(userParams);

  return userItem;
};

export const getUser = async (partitionKey: string): Promise<User> => {
  const infoParams: GetCommandInput = {
    TableName: TABLE_NAME,
    Key: {
      pk: partitionKey,
      sk: "INFO",
    },
  };

  const infoResult = await getItemDynamoDB<User>(infoParams);
  if (!infoResult) {
    console.error("Failed to find user info.");
    throw new Error("Failed to find user info.");
  }

  return infoResult.Item;
};

export const updateUser = async (
  partitionKey: string,
  user: HubspotUser,
): Promise<void> => {
  const {
    firstname,
    lastname,
    iagl_baecmemberid,
    email,
    lastmodifieddate,
    isPolicyActive,
    hs_object_id,
    cuid
  } = user;
  const updateParams: UpdateCommandInput = {
    TableName: TABLE_NAME,
    Key: {
      pk: partitionKey,
      sk: "INFO",
    },
    UpdateExpression:
      "SET #firstName = :firstName, #lastName = :lastName, #email = :email, #baecMemberId = :baecMemberId, #lastModifiedDate = :lastModifiedDate, #isPolicyActive = :isPolicyActive, #gsi1sk = :gsi1sk, #hubspotRecordId = :hubspotRecordId, #userId = :userId",
    ExpressionAttributeNames: {
      "#firstName": "firstName",
      "#lastName": "lastName",
      "#email": "email",
      "#baecMemberId": "baecMemberId",
      "#lastModifiedDate": "lastModifiedDate",
      "#isPolicyActive": "isPolicyActive",
      "#gsi1sk": "gsi1sk",
      "#hubspotRecordId": "hubspotRecordId",
      "#userId": "userId",
    },
    ExpressionAttributeValues: {
      ":firstName": firstname,
      ":lastName": lastname,
      ":email": email,
      ":baecMemberId": iagl_baecmemberid,
      ":lastModifiedDate": lastmodifieddate,
      ":isPolicyActive": isPolicyActive,
      ":gsi1sk": `IS_POLICY_ACTIVE#${isPolicyActive.toString().toUpperCase()}#USER#${cuid}`,
      ":hubspotRecordId": hs_object_id,
      ":userId": cuid,
    },
  };

  await updateItemDynamoDB(updateParams);
};

export const getNumberOfUsersWithPolicyActive = async (): Promise<number> => {
  const infoParams: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: "GSI1",
    KeyConditionExpression: "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1sk_prefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": "USER",
      ":gsi1sk_prefix": `IS_POLICY_ACTIVE#TRUE#USER#`,
    },
  };

  const infoResult = await queryDynamoDB<User>(infoParams);
  return infoResult.Items?.length ?? 0;
};

export const putUserPlayingDrawLock = async (
  userId: string,
  drawId: string,
): Promise<void> => {
  const currentTime = Math.floor(Date.now() / 1000);
  const lockItem = {
    pk: `USER#${userId}`,
    sk: `DRAW_LOCK#${drawId}`,
    userId,
    drawId,
    expiresAt: currentTime + 10,
  };

  await putItemDynamoDB({
    TableName: TABLE_NAME,
    Item: lockItem,
    ConditionExpression: "attribute_not_exists(pk) OR attribute_not_exists(sk) OR #expiresAt <= :currentTime",
    ExpressionAttributeNames: {
      "#expiresAt": "expiresAt"
    },
    ExpressionAttributeValues: {
      ":currentTime": currentTime,
    },
  });
};

export const removeUserPlayingDrawLockTransaction = async (userId: string, drawId: string) => {
  await deleteItemDynamoDB({
    TableName: TABLE_NAME,
    Key: {
      pk: `USER#${userId}`,
      sk: `DRAW_LOCK#${drawId}`,
    },
  });
};

