import { v4 as uuidv4 } from "uuid";
import { getEnvVariable } from "../helpers/getEnvVariable";
import { putItemDynamoDB, queryDynamoDB } from "../lambdas/helpers";
import { QueryCommandInput } from "@aws-sdk/lib-dynamodb";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

interface TravelPlanSubmission {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  gsi2pk: string;
  gsi2sk: string;
  dateSubmitted: string;
  userId: string;
  formSubmittedId: string;
  prizeDrawId: string;
  formId: string;
}

export const queryTravelPlanSubmissionsForPrizeDraw = async ({ userId, prizeDrawId }: { userId: string, prizeDrawId: string }) => {
  const formsSubmittedParams: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: "GSI1",
    KeyConditionExpression: "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1skPrefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": `USER#${userId}`,
      ":gsi1skPrefix": `TRAVEL_PLAN_SUBMISSION#PRIZE_DRAW_ID#${prizeDrawId}`,
    },
  };

  const formsSubmittedResult =
    await queryDynamoDB<TravelPlanSubmission>(formsSubmittedParams);

  return formsSubmittedResult.Items ?? [];
}

export const queryAllTravelPlanSubmissions = async (userId: string) => {
  const formsSubmittedParams: QueryCommandInput = {
    TableName: TABLE_NAME,
    KeyConditionExpression: "pk = :pk AND begins_with(sk, :skPrefix)",
    ExpressionAttributeValues: {
      ":pk": `USER#${userId}`,
      ":skPrefix": "TRAVEL_PLAN_SUBMISSION#",
    },
  };

  const formsSubmittedResult =
    await queryDynamoDB<TravelPlanSubmission>(formsSubmittedParams);

  return formsSubmittedResult.Items ?? [];
};

export const getMostRecentTravelPlanSubmission = async (userId: string): Promise<TravelPlanSubmission | null> => {
  const params: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: "GSI2",
    KeyConditionExpression: "gsi2pk = :gsi2pk",
    ExpressionAttributeValues: {
      ":gsi2pk": `USER#${userId}`,
    },
    ScanIndexForward: false,
    Limit: 1,
  };

  const result = await queryDynamoDB<TravelPlanSubmission>(params);
  return result.Items?.[0] ?? null;
};

export const createTravelPlanSubmission = async (
  { userId, prizeDrawId, formId }: { userId: string, prizeDrawId: string, formId: string },
): Promise<TravelPlanSubmission> => {
  const now = new Date().toISOString();
  const userPk = `USER#${userId}`;
  const formSubmittedId = uuidv4();
  
  const travelPlanItem: TravelPlanSubmission = {
    pk: userPk,
    sk: `TRAVEL_PLAN_SUBMISSION#${formSubmittedId}`,
    gsi1pk: userPk,
    gsi1sk: `TRAVEL_PLAN_SUBMISSION#PRIZE_DRAW_ID#${prizeDrawId}#FORM_ID#${formId.toLocaleUpperCase()}#FORM_SUBMITTED_ID#${formSubmittedId}`,
    gsi2pk: userPk,
    gsi2sk: `TRAVEL_PLAN_SUBMISSION_DATE#${now}#FORM_SUBMITTED_ID#${formSubmittedId}`,
    dateSubmitted: now,
    userId,
    formSubmittedId,
    prizeDrawId,
    formId,
  };

  const travelPlansParams = {
    TableName: TABLE_NAME,
    Item: travelPlanItem,
  };

  await putItemDynamoDB(travelPlansParams);

  return travelPlanItem;
}