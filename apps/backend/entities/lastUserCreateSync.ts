import { getEnvVariable } from "../helpers/getEnvVariable";
import { getItemDynamoDB, putItemDynamoDB } from "../lambdas/helpers";

export interface LastUserCreateSync {
  pk: string;
  sk: string;
  date: string;
}

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

export const updateLastUserCreateSync = async (): Promise<void> => {
  await putItemDynamoDB({
    TableName: TABLE_NAME,
    Item: {
      pk: "LAST_USER_CREATE_SYNC",
      sk: "INFO",
      date: new Date().toISOString(),
    },
  });
};

export const getLastUserCreateSync = async (): Promise<LastUserCreateSync> => {
  const response = await getItemDynamoDB<LastUserCreateSync>({
    TableName: TABLE_NAME,
    Key: {
      pk: "LAST_USER_CREATE_SYNC",
      sk: "INFO",
    },
  });

  return response.Item;
};
