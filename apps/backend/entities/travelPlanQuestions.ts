import { GetCommandInput, QueryCommandInput } from '@aws-sdk/lib-dynamodb';
import { getEnvVariable } from '../helpers/getEnvVariable';
import { batchWriteItemsDynamoDB, deleteItemDynamoDB, queryDynamoDB, getItemDynamoDB } from '../lambdas/helpers';
import { HubspotTravelPlanQuestion } from '../services/hubspot/travelPlanQuestions';

const TABLE_NAME = getEnvVariable('PLATFORM_TABLE');

export interface TravelPlanQuestion {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  question: string;
  questionFormat: "Text Input" | "Date Picker Range" | "Date Select Day" | "Combobox";
  questionPlaceholderText?: string;
  questionId: string;
  questionName: string;
  travelPlanFormId: string | undefined;
  hubspotRecordId: string;
  hubspotCreatedDate: string;
}

export const updateTravelPlanQuestionsFromHubspot = async ({
  questions,
}: {
  questions: HubspotTravelPlanQuestion[];
}) => {
  console.log(`Updating ${questions.length} travel plan questions from Hubspot`);
  const BATCH_SIZE = 25; // DynamoDB batch write limit

  for (let i = 0; i < questions.length; i += BATCH_SIZE) {
    const batch = questions.slice(i, i + BATCH_SIZE);

    const writeRequests = batch.map((question) => {
      const questionItem: TravelPlanQuestion = {
        pk: `TRAVEL_PLAN_FORM_QUESTION#${question.travel_plan_question_cuid}`,
        sk: `TRAVEL_PLAN_FORM_QUESTION#${question.travel_plan_question_cuid}`,
        gsi1pk: "TRAVEL_PLAN_FORM_QUESTION",
        gsi1sk: `TRAVEL_PLAN_FORM#${question.travel_plan_form_cuid}#HUBSPOT_CREATED_DATE#${question.hs_createdate}#TRAVEL_PLAN_FORM_QUESTION#${question.travel_plan_question_cuid}`,
        questionId: question.travel_plan_question_cuid,
        questionName: question.question_name,
        question: question.question_text,
        questionFormat: question.question_format as "Text Input" | "Date Picker Range" | "Date Select Day" | "Combobox",
        questionPlaceholderText: question.question_placeholder_text,
        travelPlanFormId: question.travel_plan_form_cuid,
        hubspotRecordId: question.hs_object_id,
        hubspotCreatedDate: question.hs_createdate,
      };

      return {
        PutRequest: {
          Item: questionItem,
        },
      };
    });

    await batchWriteItemsDynamoDB({
      RequestItems: {
        [TABLE_NAME]: writeRequests,
      },
    });
  }
};

export const deleteTravelPlanQuestion = async (questionId: string) => {
  await deleteItemDynamoDB({
    TableName: TABLE_NAME,
    Key: {
      pk: `TRAVEL_PLAN_FORM_QUESTION#${questionId}`,
      sk: `TRAVEL_PLAN_FORM_QUESTION#${questionId}`,
    },
  });
};

export const batchDeleteTravelPlanQuestions = async (questionIds: string[]) => {
  await Promise.all(
    questionIds.map((questionId) => deleteTravelPlanQuestion(questionId)),
  );
};  

export const queryTravelPlanQuestionsForForm = async (travelPlanFormId: string) => {
  const params: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: 'GSI1',
    KeyConditionExpression: "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1skPrefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": "TRAVEL_PLAN_FORM_QUESTION",
      ":gsi1skPrefix": `TRAVEL_PLAN_FORM#${travelPlanFormId}`
    },
  }

  const questionsResult = await queryDynamoDB<TravelPlanQuestion>(params);
  return questionsResult.Items ?? [];
};


export const getTravelPlanQuestion = async (questionId: string): Promise<TravelPlanQuestion | undefined> => {
  const travelPlanQuestionParams: GetCommandInput = {
    TableName: TABLE_NAME,
    Key: {
      pk: `TRAVEL_PLAN_FORM_QUESTION#${questionId}`,
      sk: `TRAVEL_PLAN_FORM_QUESTION#${questionId}`
    }
  }

  const travelPlanQuestionResult = await getItemDynamoDB<TravelPlanQuestion>(travelPlanQuestionParams);

  return travelPlanQuestionResult.Item;
};