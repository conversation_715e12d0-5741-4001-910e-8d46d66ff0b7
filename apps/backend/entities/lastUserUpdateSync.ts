import { getEnvVariable } from "../helpers/getEnvVariable";
import { getItemDynamoDB, putItemDynamoDB } from "../lambdas/helpers";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

export interface LastUserUpdateSync {
  pk: string;
  sk: string;
  date: string;
}

export const updateLastUserUpdateSync = async (): Promise<void> => {
  await putItemDynamoDB({
    TableName: TABLE_NAME,
    Item: {
      pk: "LAST_USER_UPDATE_SYNC",
      sk: "INFO",
      date: new Date().toISOString(),
    },
  });
};

export const getLastUserUpdateSync = async (): Promise<LastUserUpdateSync> => {
  const response = await getItemDynamoDB<LastUserUpdateSync>({
    TableName: TABLE_NAME,
    Key: {
      pk: "LAST_USER_UPDATE_SYNC",
      sk: "INFO",
    },
  });

  return response.Item;
};
