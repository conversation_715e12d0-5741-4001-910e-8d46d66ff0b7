import { putItemDynamoDB, getItemDynamoDB, updateItemDynamoDB } from "../lambdas/helpers";
import { getEnvVariable } from "../helpers/getEnvVariable";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

interface ActiveDrawPointerItem {
  pk: string;
  sk: string;
  prizeDrawId: string;
  updatedAt: string;
  isActive: boolean;
}

interface PrizeDrawItem {
  pk: string;
  sk: string;
  prizeDrawId: string;
  startDate: string;
  endDate: string;
  prizeDrawName: string;
  hubspotRecordId: string;
  prizeSlotBuffer: number;
}

interface PrizeDrawItem {
  pk: string;
  sk: string;
  startDate: string;
  endDate: string;
  prizeDrawId: string;
  prizeDrawName: string;
  hubspotRecordId: string;
}

export const putPrizeDraw = async ({
  prizeDrawId,
  startDate,
  endDate,
  prizeDrawName,
  recordId,
  prizeSlotBuffer
}: {
  prizeDrawId: string;
  startDate: string;
  endDate: string;
  prizeDrawName: string;
  recordId: string;
  prizeSlotBuffer: number;
}) => {
  const prizeDrawItem = {
    pk: `PRIZE_DRAW#${prizeDrawId}`,
    sk: `PRIZE_DRAW#${prizeDrawId}`,
    startDate,
    endDate,
    prizeDrawId,
    prizeDrawName,
    hubspotRecordId: recordId,
    prizeSlotBuffer
  };

  await putItemDynamoDB({ TableName: TABLE_NAME, Item: prizeDrawItem });
};

export const putActivePrizeDrawPointer = async (prizeDrawId: string) => {
  const activeDrawPointer = {
    pk: "PRIZE_DRAW_POINTER",
    sk: "ACTIVE_DRAW",
    prizeDrawId,
    updatedAt: new Date().toISOString(),
    isActive: true
  };
  await putItemDynamoDB({ TableName: TABLE_NAME, Item: activeDrawPointer });
};

export const getActivePrizeDrawId = async () => {
  const activeDrawPointer = await getItemDynamoDB<ActiveDrawPointerItem>({
    TableName: TABLE_NAME,
    Key: {
      pk: "PRIZE_DRAW_POINTER",
      sk: "ACTIVE_DRAW",
    },
  });

  if (!activeDrawPointer.Item) {
    return { prizeDrawId: null, isActive: false };
  }

  return { prizeDrawId: activeDrawPointer.Item.prizeDrawId, isActive: activeDrawPointer.Item.isActive };
};

export const updatePrizeDrawPrizeSlotBuffer = async ({prizeDrawId, prizeSlotBuffer}: {prizeDrawId: string, prizeSlotBuffer: number}) => {
  await updateItemDynamoDB({ 
    TableName: TABLE_NAME, 
    Key: { 
      pk: `PRIZE_DRAW#${prizeDrawId}`, 
      sk: `PRIZE_DRAW#${prizeDrawId}`
    }, 
    UpdateExpression: "set prizeSlotBuffer = :val", 
    ExpressionAttributeValues: { 
      ":val": prizeSlotBuffer 
    } 
  });
};

export const getPrizeDraw = async (prizeDrawId: string) => {
  const prizeDraw = await getItemDynamoDB<PrizeDrawItem>({
    TableName: TABLE_NAME,
    Key: { pk: `PRIZE_DRAW#${prizeDrawId}`, sk: `PRIZE_DRAW#${prizeDrawId}` },
  });
  return prizeDraw.Item;
};
