import { getEnvVariable } from '../helpers/getEnvVariable';
import { getItemDynamoDB, putItemDynamoDB } from '../lambdas/helpers';

const TABLE_NAME = getEnvVariable('PLATFORM_TABLE');

export interface LastTravelPlanFormsSync {
  pk: string;
  sk: string;
  date: string;
}

export const getLastTravelPlanFormsSync = async(): Promise<LastTravelPlanFormsSync | undefined> => {
  const response = await getItemDynamoDB<LastTravelPlanFormsSync>({
      TableName: TABLE_NAME,
      Key: {
        pk: "LAST_TRAVEL_PLAN_FORMS_SYNC",
        sk: "INFO",
      },
    });
  
    return response.Item;
};

export const updateLastTravelPlanFormsSync = async() => {
  await putItemDynamoDB({
      TableName: TABLE_NAME,
      Item: {
        pk: "LAST_TRAVEL_PLAN_FORMS_SYNC",
        sk: "INFO",
        date: new Date().toISOString(),
      },
    });
};