import { getEnvVariable } from '../helpers/getEnvVariable';
import { getItemDynamoDB, putItemDynamoDB } from '../lambdas/helpers';

const TABLE_NAME = getEnvVariable('PLATFORM_TABLE');

export interface LastTravelPlanQuestionsSync {
  pk: string;
  sk: string;
  date: string;
}

export const getLastTravelPlanQuestionsSync = async(): Promise<LastTravelPlanQuestionsSync | undefined> => {
  const response = await getItemDynamoDB<LastTravelPlanQuestionsSync>({
      TableName: TABLE_NAME,
      Key: {
        pk: "LAST_TRAVEL_PLAN_QUESTIONS_SYNC",
        sk: "INFO",
      },
    });
  
    return response.Item;
};

export const updateLastTravelPlanQuestionsSync = async() => {
  await putItemDynamoDB({
      TableName: TABLE_NAME,
      Item: {
        pk: "LAST_TRAVEL_PLAN_QUESTIONS_SYNC",
        sk: "INFO",
        date: new Date().toISOString(),
      },
    });
};