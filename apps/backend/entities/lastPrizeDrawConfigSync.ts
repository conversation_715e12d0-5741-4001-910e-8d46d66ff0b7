import { getEnvVariable } from "../helpers/getEnvVariable";
import { getItemDynamoDB, putItemDynamoDB } from "../lambdas/helpers";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

export interface LastPrizeDrawConfigSync {
  pk: string;
  sk: string;
  date: string;
}

export const updateLastPrizeDrawConfigSync = async (): Promise<void> => {
  await putItemDynamoDB({
    TableName: TABLE_NAME,
    Item: {
      pk: "LAST_PRIZE_DRAW_CONFIG_UPDATE_SYNC",
      sk: "INFO",
      date: new Date().toISOString(),
    },
  });
};

export const getLastPrizeDrawConfigSync = async (): Promise<
  LastPrizeDrawConfigSync | undefined
> => {
  const response = await getItemDynamoDB<LastPrizeDrawConfigSync>({
    TableName: TABLE_NAME,
    Key: {
      pk: "LAST_PRIZE_DRAW_CONFIG_UPDATE_SYNC",
      sk: "INFO",
    },
  });

  return response.Item;
};
