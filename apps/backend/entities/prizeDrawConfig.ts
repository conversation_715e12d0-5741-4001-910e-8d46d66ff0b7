import { putItemDynamoDB, deleteItemDynamoDB, queryDynamoDB, batchWriteItemsDynamoDB } from "../lambdas/helpers";
import { getEnvVariable } from "../helpers/getEnvVariable";
import { QueryCommandInput } from "@aws-sdk/lib-dynamodb";
import { HubspotPrizeDrawConfigWithPrizeDraw } from "../services/hubspot/prizeDrawConfigs";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

export interface PrizeConfig {
  rewardAmount: number;
  configId: string;
  prizeType: "NON_GUARANTEED" | "GUARANTEED";
  maximumNumberOfWinners: number;
  hubspotRecordId: string | undefined;
}

interface DynamoDBPrizeDrawConfig {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  prizeType: "NON_GUARANTEED" | "GUARANTEED";
  rewardAmount: number;
  maximumNumberOfWinners: number;
  configId: string;
  prizeDrawId: string | undefined;
  hubspotRecordId: string | undefined;
}

export const putPrizeDrawConfigs = async ({
  prizeDrawId,
  configs,
}: {
  prizeDrawId: string;
  configs: PrizeConfig[];
}) => {
  await Promise.all(
    configs.map((config) => {
      const configItem: DynamoDBPrizeDrawConfig = {
        pk: `PRIZE_DRAW_CONFIG_ID#${config.configId}`,
        sk: `PRIZE_DRAW_CONFIG_ID#${config.configId}`,
        gsi1pk: `PRIZE_DRAW#${prizeDrawId}`,
        gsi1sk: `PRIZE_TYPE#${config.prizeType}#PRIZE_CONFIG_ID#${config.configId}`,
        prizeType: config.prizeType,
        rewardAmount: config.rewardAmount,
        maximumNumberOfWinners: config.maximumNumberOfWinners,
        configId: config.configId,
        prizeDrawId: prizeDrawId,
        hubspotRecordId: config.hubspotRecordId,
      };

      return putItemDynamoDB({ TableName: TABLE_NAME, Item: configItem });
    }),
  );
};

export const deletePrizeDrawConfig = async (configId: string) => {
  await deleteItemDynamoDB({
    TableName: TABLE_NAME,
    Key: {
      pk: `PRIZE_DRAW_CONFIG_ID#${configId}`,
      sk: `PRIZE_DRAW_CONFIG_ID#${configId}`,
    },
  });
};

export const batchDeletePrizeDrawConfigs = async (configIds: string[]) => {
  await Promise.all(
    configIds.map((configId) => deletePrizeDrawConfig(configId)),
  );
};

export const updatePrizeDrawConfigsFromHubspot = async ({
  configs,
}: {
  configs: HubspotPrizeDrawConfigWithPrizeDraw[];
}) => {
  console.log(`Updating ${configs.length} prize draw configs from Hubspot`);
  const BATCH_SIZE = 25; // DynamoDB batch write limit

  for (let i = 0; i < configs.length; i += BATCH_SIZE) {
    const batch = configs.slice(i, i + BATCH_SIZE);

    const writeRequests = batch.map((config) => {
      const prizeType = config.guaranteed_prize === 'true' ? "GUARANTEED" : "NON_GUARANTEED";
      const prizeDrawId = config.prizeDraw?.prize_draw_reference;

      const configItem: DynamoDBPrizeDrawConfig = {
        pk: `PRIZE_DRAW_CONFIG_ID#${config.cuid}`,
        sk: `PRIZE_DRAW_CONFIG_ID#${config.cuid}`,
        gsi1pk: `PRIZE_DRAW#${config.prizeDraw?.prize_draw_reference}`,
        gsi1sk: `PRIZE_TYPE#${prizeType}#PRIZE_CONFIG_ID#${config.cuid}`,
        prizeType: prizeType,
        rewardAmount: parseInt(config.prize_size),
        maximumNumberOfWinners: parseInt(config.maximum_number_of_wins),
        configId: config.cuid,
        prizeDrawId: prizeDrawId,
        hubspotRecordId: config.hs_record_id,
      };

      return {
        PutRequest: {
          Item: configItem,
        },
      };
    });

    await batchWriteItemsDynamoDB({
      RequestItems: {
        [TABLE_NAME]: writeRequests,
      },
    });
  }
};

export const queryPrizeDrawConfigsByPrizeDraw = async (prizeDrawId: string): Promise<DynamoDBPrizeDrawConfig[]> => {
  const params: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: "GSI1",
    KeyConditionExpression: "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1sk_prefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": `PRIZE_DRAW#${prizeDrawId}`,
      ":gsi1sk_prefix": "PRIZE_TYPE#"
    }
  };

  const results = await queryDynamoDB(params);
  return results.Items as DynamoDBPrizeDrawConfig[];
};

