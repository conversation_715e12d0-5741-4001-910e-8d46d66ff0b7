import { QueryCommandInput, GetCommandInput } from '@aws-sdk/lib-dynamodb';
import { getEnvVariable } from '../helpers/getEnvVariable';
import { batchWriteItemsDynamoDB, deleteItemDynamoDB, getItemDynamoDB, queryDynamoDB } from '../lambdas/helpers';
import { HubspotTravelPlanForm } from '../services/hubspot/travelPlanForms';

const TABLE_NAME = getEnvVariable('PLATFORM_TABLE');

export interface TravelPlanForm {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  weightedProbability: number;
  travelPlanFormId: string;
  formName: string;
  formId: string;
  formTitle: string;
  hubspotRecordId: string;
}

export const queryTravelPlanForms = async () => {
  const travelPlanFormsParams: QueryCommandInput = {
    TableName: TABLE_NAME,
    IndexName: 'GSI1',
    KeyConditionExpression: "gsi1pk = :gsi1pk AND begins_with(gsi1sk, :gsi1skPrefix)",
    ExpressionAttributeValues: {
      ":gsi1pk": "TRAVEL_PLAN_FORM",
      ":gsi1skPrefix": "TRAVEL_PLAN_FORM#"
    },
  }
  const formsSubmittedResult = await queryDynamoDB<TravelPlanForm>(travelPlanFormsParams);
  
  return formsSubmittedResult.Items ?? [];
};

export const updateTravelPlanFormsFromHubspot = async ({
  forms,
}: {
  forms: HubspotTravelPlanForm[];
}) => {
  console.log(`Updating ${forms.length} travel plan forms from Hubspot`);
  const BATCH_SIZE = 25; // DynamoDB batch write limit

  for (let i = 0; i < forms.length; i += BATCH_SIZE) {
    const batch = forms.slice(i, i + BATCH_SIZE);

    const writeRequests = batch.map((form) => {
      const formItem: TravelPlanForm = {
        pk: `TRAVEL_PLAN_FORM#${form.travel_plan_form_cuid}`,
        sk: `TRAVEL_PLAN_FORM#${form.travel_plan_form_cuid}`,
        gsi1pk: "TRAVEL_PLAN_FORM",
        gsi1sk: `TRAVEL_PLAN_FORM#${form.travel_plan_form_cuid}`,
        weightedProbability: Number(form.weighted_probability),
        travelPlanFormId: form.travel_plan_form_cuid,
        hubspotRecordId: form.hs_object_id,
        formName: form.form_name,
        formId: form.travel_plan_form_cuid,
        formTitle: form.title,
      };

      return {
        PutRequest: {
          Item: formItem,
        },
      };
    });

    await batchWriteItemsDynamoDB({
      RequestItems: {
        [TABLE_NAME]: writeRequests,
      },
    });
  }
};

export const deleteTravelPlanForm = async (formId: string) => {
  await deleteItemDynamoDB({
    TableName: TABLE_NAME,
    Key: {
      pk: `TRAVEL_PLAN_FORM#${formId}`,
      sk: `TRAVEL_PLAN_FORM#${formId}`,
    },
  });
};

export const batchDeleteTravelPlanForms = async (formIds: string[]) => {
  await Promise.all(
    formIds.map((formId) => deleteTravelPlanForm(formId)),
  );
}

export const getTravelPlanForm = async (formId: string): Promise<TravelPlanForm | undefined> => {
  const travelPlanFormParams: GetCommandInput = {
    TableName: TABLE_NAME,
    Key: {
      pk: `TRAVEL_PLAN_FORM#${formId}`,
      sk: `TRAVEL_PLAN_FORM#${formId}`
    }
  }

  const travelPlanFormResult = await getItemDynamoDB<TravelPlanForm>(travelPlanFormParams);

  return travelPlanFormResult.Item;
};