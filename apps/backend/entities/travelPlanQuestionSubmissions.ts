import { v4 as uuidv4 } from "uuid";
import { getEnvVariable } from "../helpers/getEnvVariable";
import { putItemDynamoDB } from "../lambdas/helpers";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

interface TravelPlanQuestionSubmission {
  pk: string;
  sk: string;
  gsi1pk: string;
  gsi1sk: string;
  dateSubmitted: string;
  userId: string;
  answer: string;
  formSubmittedId: string;
  prizeDrawId: string;
  formId: string;
  questionId: string;
  questionSubmissionId: string;
}


interface CreateTravelPlanQuestionSubmissionInput {
  userId: string;
  formId: string;
  formSubmittedId: string;
  prizeDrawId: string;
  questionId: string;
  answer: string;
}


export const createTravelPlanQuestionSubmission = async (
  input: CreateTravelPlanQuestionSubmissionInput,
): Promise<
  { status: number; travelPlanQuestionSubmission: TravelPlanQuestionSubmission } | { status: number; error: string }
> => {
  const { userId, formId, formSubmittedId, prizeDrawId, questionId, answer } = input;
  const now = new Date().toISOString();
  const userPk = `USER#${userId}`;
  const questionSubmissionId = uuidv4();

  const travelPlanItem: TravelPlanQuestionSubmission = {
    pk: userPk,
    sk: `TRAVEL_PLAN_QUESTION_SUBMISSION#${questionSubmissionId}`,
    gsi1pk: userPk,
    gsi1sk: `FORM_SUBMITTED#FORM_SUBMISSION_ID#${formSubmittedId}#FORM_QUESTION_ID#${questionId}#TRAVEL_PLAN_QUESTION_SUBMISSION#${questionSubmissionId}`,
    questionSubmissionId,
    dateSubmitted: now,
    userId,
    answer,
    formSubmittedId,
    prizeDrawId,
    formId,
    questionId,
  };

  const travelPlansParams = {
    TableName: TABLE_NAME,
    Item: travelPlanItem,
  };

  await putItemDynamoDB(travelPlansParams);

  return { status: 201, travelPlanQuestionSubmission: travelPlanItem };
}