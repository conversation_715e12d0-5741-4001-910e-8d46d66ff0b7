import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as s3deploy from "aws-cdk-lib/aws-s3-deployment";
import * as cloudfront from "aws-cdk-lib/aws-cloudfront";
import * as cloudfrontOrigins from "aws-cdk-lib/aws-cloudfront-origins";
import * as path from "path";
import { join } from "path";
import * as events from "aws-cdk-lib/aws-events";
import { LambdaFunction } from "aws-cdk-lib/aws-events-targets";
import { PublicHostedZone } from "aws-cdk-lib/aws-route53";
import * as route53 from "aws-cdk-lib/aws-route53";
import * as route53Targets from "aws-cdk-lib/aws-route53-targets";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import { getStage } from "../helpers/getStage";
import { <PERSON><PERSON>amb<PERSON> } from "../constructs/wandaLambda/config";
import { <PERSON><PERSON><PERSON> } from "../constructs/wandaApi/config";
import { WandaDynamoDBTable } from "../constructs/wandaDynamoDBTable/config";
import { Secret } from "aws-cdk-lib/aws-secretsmanager";
import { SecretValue } from "aws-cdk-lib";
import { WandaPrizeSlotsDynamoDBTable } from "../constructs/wandaPrizeSlotsDynamoDBTable/config";
import { RetrievePrizeFromDrawSQSQueue } from "../constructs/sqs/retrievePrizeFromDraw-config";
import { InitiatePrizeDrawPlayLambda } from "../lambdas/initiatePrizeDrawPlay/config";
import { RetrievePrizeFromDrawLambda } from "../lambdas/retrievePrizeFromDraw/config";
import { GetPrizeDrawPlayResultLambda } from "../lambdas/getPrizeDrawPlayResult/config";
import { StartPrizeDrawLambda } from "../lambdas/startPrizeDraw/config";
import { AddGuaranteedPrizeToSlotsLambda } from "../lambdas/addGuaranteedPrizeToSlots/config";
import { CreatePrizeDrawLambda } from "../lambdas/createPrizeDraw/config";
import { DynamoStreamHandlerLambda } from "../lambdas/dynamoStreamHandler/config";
import { SendPrizeDrawPlayToHubspotSQSQueue } from "../constructs/sqs/sendPrizeDrawPlayToHubspot-config";
import { SendTravelPlanQuestionSubmissionToHubspotSQSQueue } from "../constructs/sqs/sendTravelPlanQuestionSubmissionToHubspot-config";
import { SendPrizeDrawPlayToHubspotLambda } from '../lambdas/sendPrizeDrawPlayToHubspot/config';
import { SendTravelPlanQuestionSubmissionToHubspotLambda } from '../lambdas/sendTravelPlanQuestionSubmissionToHubspot/config';
import { FetchTravelPlanFormsLambda } from '../lambdas/fetchTravelPlanForms/config';
import { FetchTravelPlanQuestionsLambda } from '../lambdas/fetchTravelPlanQuestions/config';
import { GetTravelPlanQuestionsForFormLambda } from "../lambdas/getTravelPlanQuestionsForForm/config";
import { FetchTravelPlanComboboxLambda } from '../lambdas/fetchTravelPlanComboboxes/config';
import * as sfn from "aws-cdk-lib/aws-stepfunctions";
import * as tasks from "aws-cdk-lib/aws-stepfunctions-tasks";
import * as targets from "aws-cdk-lib/aws-events-targets";
import { WandaWebAcl } from "../constructs/waf/waf";
import * as cloudwatch from "aws-cdk-lib/aws-cloudwatch";
import * as sns from "aws-cdk-lib/aws-sns";
import * as subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import * as cwActions from "aws-cdk-lib/aws-cloudwatch-actions";

// Constant for alarm configuration
const DEFAULT_ALARM_EMAILS = '<EMAIL>';

export class BackendStack extends cdk.Stack {
  private cfnOutCloudFrontUrl: cdk.CfnOutput;

  private cfnOutDistributionId: cdk.CfnOutput;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);
    const { isProd, stage } = getStage(this);

    if (stage === 'staging') {
      //const BUCKET_NAME = "staging-ventures-wanda-prizedraw";
      const BUCKET_NAME = process.env.BUCKET_NAME;

      // Create a bucket for S3 access logs for the static website
      const staticWebsiteLogsBucket = new s3.Bucket(this, 'StagingStaticWebsiteLogsBucket', {
        removalPolicy: cdk.RemovalPolicy.RETAIN,
        versioned: true,
        encryption: s3.BucketEncryption.S3_MANAGED,
        lifecycleRules: [
          {
            expiration: cdk.Duration.days(90),
          },
        ],
      });

      const bucket = new s3.Bucket(this, "StagingStaticWebsiteBucket", {
        bucketName: BUCKET_NAME,
        blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
        publicReadAccess: false,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        serverAccessLogsBucket: staticWebsiteLogsBucket,
        serverAccessLogsPrefix: 'access-logs/',
      });

      new s3deploy.BucketDeployment(this, "StagingDeployWebsite", {
        sources: [s3deploy.Source.asset(path.join(__dirname, "../../frontend/dist"))],
        destinationBucket: bucket,
      });

      const distribution = new cloudfront.Distribution(this, "WandaWebsiteDistribution", {
        defaultBehavior: {
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
          compress: true,
          origin: new cloudfrontOrigins.S3Origin(bucket),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        },
        defaultRootObject: "index.html",
        errorResponses: [
          {
            httpStatus: 403,
            responseHttpStatus: 200,
            responsePagePath: "/index.html",
            ttl: cdk.Duration.minutes(30),
          },
        ],
        minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2019,
      });

      // Output the CloudFront URL for easy access
      new cdk.CfnOutput(this, "CloudFrontURL", {
        value: distribution.distributionDomainName,
        description: "CloudFront URL for the frontend",
      });

      this.cfnOutDistributionId = new cdk.CfnOutput(
        this,
        "CfnOutDistributionId",
        {
          value: distribution.distributionId,
          description: "CloudFront Distribution Id",
        },
      );
    }

    if (isProd) {
      const hostedZone = PublicHostedZone.fromHostedZoneAttributes(
        this,
        "HostedZone",
        {
          hostedZoneId: "Z0711203395VSAP3KW3DP",
          zoneName: "prizedraw-prd.wanda.world",
        },
      );

      const certificate = acm.Certificate.fromCertificateArn(
        this,
        "SiteCertificate",
        "arn:aws:acm:us-east-1:182399697519:certificate/3c504933-8b8f-4b7d-9478-04874714609a",
      );

      const BUCKET_NAME = "ventures-wanda-prizedraw";

      // Create a bucket for S3 access logs for the static website
      const staticWebsiteLogsBucket = new s3.Bucket(this, 'StaticWebsiteLogsBucket', {
        removalPolicy: cdk.RemovalPolicy.RETAIN,
        versioned: true,
        encryption: s3.BucketEncryption.S3_MANAGED,
        lifecycleRules: [
          {
            expiration: cdk.Duration.days(90),
          },
        ],
      });

      const bucket = new s3.Bucket(this, "StaticWebsiteBucket", {
        bucketName: BUCKET_NAME,
        blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
        publicReadAccess: false,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        serverAccessLogsBucket: staticWebsiteLogsBucket,
        serverAccessLogsPrefix: 'access-logs/',
      });

      new s3deploy.BucketDeployment(this, "DeployWebsite", {
        sources: [
          s3deploy.Source.asset(path.join(__dirname, "../../frontend/dist")),
        ], // Path to the directory containing the index.html file
        destinationBucket: bucket,
      });

      const distribution = new cloudfront.Distribution(
        this,
        "WandaWebsiteDistribution",
        {
          defaultBehavior: {
            allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
            compress: true,
            origin: new cloudfrontOrigins.S3Origin(bucket),
            viewerProtocolPolicy:
              cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          },
          defaultRootObject: "index.html",
          domainNames: ["prizedraw-prd.wanda.world"],
          certificate,
          errorResponses: [
            {
              httpStatus: 403,
              responseHttpStatus: 200,
              responsePagePath: "/index.html",
              ttl: cdk.Duration.minutes(30),
            },
          ],
          minimumProtocolVersion:
            cloudfront.SecurityPolicyProtocol.TLS_V1_2_2019,
        },
      );

      new route53.ARecord(this, "AliasRecord", {
        zone: hostedZone,
        recordName: "prizedraw-prd.wanda.world", // Root domain
        target: route53.RecordTarget.fromAlias(
          new route53Targets.CloudFrontTarget(distribution),
        ),
        ttl: cdk.Duration.minutes(1),
      });

      // new route53.ARecord(this, "AliasRecordWWW", {
      //   zone: hostedZone,
      //   recordName: "www.prizedraw.wanda.world", // Subdomain
      //   target: route53.RecordTarget.fromAlias(
      //     new route53Targets.CloudFrontTarget(distribution),
      //   ),
      //   ttl: cdk.Duration.minutes(1),
      // });

      this.cfnOutCloudFrontUrl = new cdk.CfnOutput(
        this,
        "CfnOutCloudFrontUrl",
        {
          value: `https://${distribution.distributionDomainName}`,
          description: "The CloudFront URL",
        },
      );

      this.cfnOutDistributionId = new cdk.CfnOutput(
        this,
        "CfnOutDistributionId",
        {
          value: distribution.distributionId,
          description: "CloudFront Distribution Id",
        },
      );
    }

    // Backend deployment:

    const stageName = isProd ? "prod" : stage;

    const dynamoTable = new WandaDynamoDBTable(
      this,
      `${stage}WandaPrizeDrawTable`,
    );
    const dynamoTablePrizeSlots = new WandaPrizeSlotsDynamoDBTable(
      this,
      `${stage}WandaPrizeSlotsTable`,
    );

    const hubspotApiTokenSecret = new Secret(
      this,
      `${stage}HubspotApiTokenSecret`,
      {
        secretStringValue: SecretValue.unsafePlainText(
          process.env?.HUBSPOT_API_TOKEN_SECRET_NAME || "",
        ),
      },
    );

    const addGuaranteedPrizeToSlotsLambda = new AddGuaranteedPrizeToSlotsLambda(
      this,
      {
        prizeSlotTable: dynamoTablePrizeSlots,
        stageName: stageName,
      },
    );

    const startPrizeDrawLambda = new StartPrizeDrawLambda(this, {
      platformTable: dynamoTable,
      prizeSlotTable: dynamoTablePrizeSlots,
      addGuaranteedPrizeToSlotsLambda: addGuaranteedPrizeToSlotsLambda,
      hubspotApiTokenSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });

    const createPrizeDrawLambda = new CreatePrizeDrawLambda(this, {
      platformTable: dynamoTable,
      startPrizeDrawLambda: startPrizeDrawLambda,
      hubspotApiTokenSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });

    const retrievePrizeFromDrawQueue = new RetrievePrizeFromDrawSQSQueue(this);

    const initiatePrizeDrawPlayLambda = new InitiatePrizeDrawPlayLambda(this, {
      platformTable: dynamoTable,
      retrievePrizeFromDrawQueueUrl: retrievePrizeFromDrawQueue.queue.queueUrl,
    });

    new RetrievePrizeFromDrawLambda(this, {
      platformTable: dynamoTable,
      prizeSlotTable: dynamoTablePrizeSlots,
      sourceQueue: retrievePrizeFromDrawQueue.queue,
      stageName: stageName,
    });

    const getGameLambda = new WandaLambda(this, `${stage}GetGameLambda`, {
      tableName: dynamoTable.tableName,
      lambdaEntry: join(__dirname, "..", "lambdas", "getGame", "handler.ts"),
      stageName: stageName,
    });

    const submitTravelPlansLambda = new WandaLambda(
      this,
      `${stage}SubmitTravelPlansLambda`,
      {
        tableName: dynamoTable.tableName,
        lambdaEntry: join(
          __dirname,
          "..",
          "lambdas",
          "submitTravelPlans",
          "handler.ts",
        ),
        stageName: stageName,
      },
    );

    const fetchNewUsersLambda = new WandaLambda(
      this,
      `${stage}FetchNewUsersLambda`,
      {
        tableName: dynamoTable.tableName,
        lambdaEntry: join(
          __dirname,
          "..",
          "lambdas",
          "fetchNewUsers",
          "handler.ts",
        ),
        secret: hubspotApiTokenSecret,
        stageName: isProd ? "prod" : stage,
      },
    );

    const getPrizeDrawPlayResultLambda = new GetPrizeDrawPlayResultLambda(
      this,
      {
        platformTable: dynamoTable,
        stageName: stageName,
      },
    );

    const fetchUpdatedUsersLambda = new WandaLambda(
      this,
      `${stage}FetchUpdatedUsersLambda`,
      {
        tableName: dynamoTable.tableName,
        lambdaEntry: join(
          __dirname,
          "..",
          "lambdas",
          "fetchUpdatedUsers",
          "handler.ts",
        ),
        secret: hubspotApiTokenSecret,
        stageName: stageName,
      },
    );

    const sendPrizeDrawPlayToHubspotQueue = new SendPrizeDrawPlayToHubspotSQSQueue(this);
    const sendTravelPlanQuestionSubmissionToHubspotQueue = new SendTravelPlanQuestionSubmissionToHubspotSQSQueue(this);

    const dynamoStreamHandlerLambda = new DynamoStreamHandlerLambda(this, {
      platformTable: dynamoTable,
      prizeDrawPlayQueue: sendPrizeDrawPlayToHubspotQueue.queue,
      travelPlanQuestionSubmissionQueue: sendTravelPlanQuestionSubmissionToHubspotQueue.queue,
      stageName: stageName,
    });

    const sendPrizeDrawPlayToHubspotLambda = new SendPrizeDrawPlayToHubspotLambda(this, {
      sourceQueue: sendPrizeDrawPlayToHubspotQueue.queue,
      platformTable: dynamoTable,
      hubspotSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });

    const sendTravelPlanQuestionSubmissionToHubspotLambda = new SendTravelPlanQuestionSubmissionToHubspotLambda(this, {
      platformTable: dynamoTable,
      sourceQueue: sendTravelPlanQuestionSubmissionToHubspotQueue.queue,
      hubspotSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });

    const fetchTravelPlanFormsLambda = new FetchTravelPlanFormsLambda(this, {
      platformTable: dynamoTable,
      hubspotSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });
    const getTravelPlanQuestionsForFormLambda = new GetTravelPlanQuestionsForFormLambda(this, {
      platformTable: dynamoTable,
      stageName: stageName,
    });


    const fetchTravelPlanQuestionsFromHubspotLambda = new FetchTravelPlanQuestionsLambda(this, {
      platformTable: dynamoTable,
      hubspotSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });

    const fetchTravelPlanComboboxesFromHubspotLambda = new FetchTravelPlanComboboxLambda(this, {
      platformTable: dynamoTable,
      hubspotSecret: hubspotApiTokenSecret,
      stageName: stageName,
    });

    dynamoTable.grantReadWriteData(getGameLambda);
    dynamoTable.grantReadWriteData(submitTravelPlansLambda);
    dynamoTable.grantReadWriteData(fetchNewUsersLambda);
    dynamoTable.grantReadWriteData(fetchUpdatedUsersLambda);
    dynamoTable.grantReadData(dynamoStreamHandlerLambda);
    dynamoTable.grantReadData(sendPrizeDrawPlayToHubspotLambda);
    dynamoTable.grantReadWriteData(sendTravelPlanQuestionSubmissionToHubspotLambda)
    dynamoTable.grantReadWriteData(fetchTravelPlanFormsLambda);
    dynamoTable.grantReadWriteData(fetchTravelPlanQuestionsFromHubspotLambda);
    dynamoTable.grantReadWriteData(fetchTravelPlanComboboxesFromHubspotLambda);

    const api =new WandaApi(this, `${stage}WandaPrizeDrawApi`, {
      getGameLambda,
      submitTravelPlansLambda,
      initiatePrizeDrawPlayLambda,
      getPrizeDrawPlayResultLambda,
      getTravelPlanQuestionsForFormLambda,
    });

    // CloudWatch Alarm for Wanda API traffic
    const apiAlarmTopic = new sns.Topic(this, `${stage}WandaApiTrafficAlarmTopic`);

    // Add email subscriptions to the SNS topic
    getEmails().forEach(email => {
      apiAlarmTopic.addSubscription(new subscriptions.EmailSubscription(email));
    });

    const apiTrafficAlarm = new cloudwatch.Alarm(this, `${stage}WandaApiTrafficAlarm`, {
      metric: api.metricCount({
        period: cdk.Duration.minutes(1),
        statistic: "Sum",
      }),
      threshold: 30,
      evaluationPeriods: 1,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      alarmDescription: "Wanda API traffic exceeded 30 requests per minute",
    });
    apiTrafficAlarm.addAlarmAction(new cwActions.SnsAction(apiAlarmTopic));

    const waf = new WandaWebAcl(this, `${stage}WandaPrizeDrawWebAcl`, {
      rateLimits: [
        {
          limit: 1000,
        },
      ],
    });

    waf.associate(api.deploymentStage.stageArn);

    
    // Cron for fetching users every 10 minutes
    const fetchUsersRule = new events.Rule(this, "FetchUsersRule", {
      schedule: events.Schedule.expression("cron(0/10 * * * ? *)"),
    });
    fetchUsersRule.addTarget(new LambdaFunction(fetchNewUsersLambda));
    fetchUsersRule.addTarget(
      new LambdaFunction(fetchUpdatedUsersLambda),
    );

    // Schedule createPrizeDraw to run 5 at midnight on the 20th of each month
    const createPrizeDrawRule = new events.Rule(this, "CreatePrizeDrawRule", {
      schedule: events.Schedule.expression("cron(0 0 20 * ? *)"),
    });
    createPrizeDrawRule.addTarget(
      new LambdaFunction(createPrizeDrawLambda),
    );

    const fetchTravelPlanStateMachine = new sfn.StateMachine(this, 'FetchTravelPlanStateMachine', {
      definition: sfn.Chain
        .start(new tasks.LambdaInvoke(this, 'FetchForms', { lambdaFunction: fetchTravelPlanFormsLambda }))
        .next(new tasks.LambdaInvoke(this, 'FetchQuestions', { lambdaFunction: fetchTravelPlanQuestionsFromHubspotLambda }))
        .next(new tasks.LambdaInvoke(this, 'FetchComboboxes', { lambdaFunction: fetchTravelPlanComboboxesFromHubspotLambda }))
    });

    const fetchTravelPlanRule = new events.Rule(this, "FetchTravelPlanRule", {
      // Original hourly schedule:
      schedule: events.Schedule.expression("cron(0 0/1 * * ? *)"),
    });
    fetchTravelPlanRule.addTarget(new targets.SfnStateMachine(fetchTravelPlanStateMachine));
  }
}

const getEmails = (): string[] => {
  return (process.env.ALARM_EMAILS || DEFAULT_ALARM_EMAILS).split(',').map(email => email.trim());
};
