# Backend CDK Project

This project contains the AWS CDK infrastructure code for the backend services.

## Prerequisites

- [Node.js](https://nodejs.org/)
- [pnpm](https://pnpm.io/)
- AWS credentials (we recommend using [Leapp](https://www.leapp.cloud/) for managing AWS credentials)

## Getting Started

1. Install dependencies:

```bash
pnpm install
```

2. Deploy your local development stack:

```bash
STAGE=<your_name> HUBSPOT_API_TOKEN_SECRET_NAME=<secret_key> pnpm run deploy -c stage=<your_name>
```
