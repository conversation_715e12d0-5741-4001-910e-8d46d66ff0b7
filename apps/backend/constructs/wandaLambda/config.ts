import { Runtime } from "aws-cdk-lib/aws-lambda";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import { Construct } from "constructs";
import * as iam from "aws-cdk-lib/aws-iam";
import { Secret } from "aws-cdk-lib/aws-secretsmanager";
import { Duration } from "aws-cdk-lib";

interface WandaLambdaProps {
  tableName: string;
  lambdaEntry: string;
  stageName: string;
  secret?: Secret;
}

export class WandaLambda extends NodejsFunction {
  constructor(scope: Construct, id: string, props: WandaLambdaProps) {
    const { tableName, lambdaEntry, secret, stageName } = props;

    super(scope, id, {
      bundling: {
        externalModules: [
          "aws-sdk", // Use the 'aws-sdk' available in the Lambda runtime
        ],
      },
      environment: {
        PLATFORM_TABLE: tableName,
        STAGE_NAME: stageName,
        ...(secret && { HUBSPOT_API_TOKEN_SECRET_NAME: secret.secretName }),
      },
      runtime: Runtime.NODEJS_20_X,
      entry: lambdaEntry,
      functionName: id,
      timeout: Duration.seconds(30),
    });

    // Add Secrets Manager permissions if secretName is provided
    if (secret) {
      this.addToRolePolicy(
        new iam.PolicyStatement({
          actions: ["secretsmanager:GetSecretValue"],
          resources: [secret.secretArn],
        }),
      );
    }
  }
}
