import { Removal<PERSON><PERSON>y, Stack } from "aws-cdk-lib";
import { LogGroup } from "aws-cdk-lib/aws-logs";
import {
  CfnLoggingConfiguration,
  CfnWebACL,
  CfnWebACLAssociation,
} from "aws-cdk-lib/aws-wafv2";
import { IConstruct } from "constructs";

/**
 * An aggregated rate limit for a WebACL
 */
export interface WandaWebAclRateLimit {
  /**
   * The maximum number of requests allowed per second.
   */
  limit: number,
}

/**
 * Props for the WandaWebACL construct
 */
export interface WandaWebACLProps {
  /**
   * A set of aggregated rate limits to apply to the WebACL
   */
  rateLimits: WandaWebAclRateLimit[];
}

/**
 * Extends the `CfnWebACL` class to create a customized Web Application Firewall with sensible defaults. This class automatically sets up the AWS Common Rule Set and Known Bad Inputs Rule Set. Additionally, it includes a rate limiting rule to protect against excessive client requests.
 *
 * @remarks
 * The `WandaWebAcl` class is designed to be associated with an API Gateway deployment stage via the `associate` method.
 *
 * @example
 * // Usage in a CDK stack:
 * const api = new WandaRestApi(this, 'MyApi');
 * const webAcl = new WandaWebAcl(this, 'MyWebAcl', {
 *  rateLimits: [{
 *    limit: 1375,
 *  }]
 * });
 * webAcl.associate(api.deploymentStage.stageArn);
 *
 * @extends CfnWebACL
 * @param {IConstruct} scope - The parent constructing scope.
 * @param {string} id - A unique identifier for the WebACL within the CDK stack.
 */

export class WandaWebAcl extends CfnWebACL {
  /**
   * The log group to store information on the traffic analysed by the WebACL.
   */
  readonly logGroup: LogGroup;

  constructor(scope: IConstruct, id: string, props: WandaWebACLProps) {
    super(scope, id, {
      scope: "REGIONAL",
      defaultAction: {
        allow: {},
      },
      visibilityConfig: {
        cloudWatchMetricsEnabled: true,
        metricName: "WandaWebACL",
        sampledRequestsEnabled: false,
      },
      customResponseBodies: {
        tooManyRequests: {
          contentType: "APPLICATION_JSON",
          content: JSON.stringify({
            statusCode: 429,
            description: "Too many requests",
          }),
        },
      },
      rules: [
        WandaWebAcl.buildManagedRule("CRS", 0, "AWSManagedRulesCommonRuleSet", [
          "SizeRestrictions_BODY",
        ]),
        WandaWebAcl.buildManagedRule(
          "KBI",
          1,
          "AWSManagedRulesKnownBadInputsRuleSet"
        ),
      ],
    });

    for (const rateLimit of props.rateLimits) {
      this.addRateLimit(rateLimit);
    }

    this.logGroup = new LogGroup(this, "LogGroup", {
      logGroupName: `aws-waf-logs-${Stack.of(this).stackName}`,
      removalPolicy: RemovalPolicy.DESTROY,
    });

    new CfnLoggingConfiguration(this, "LoggingConfiguration", {
      resourceArn: this.attrArn,
      logDestinationConfigs: [this.logGroup.logGroupArn],
    });
  }

  private addRateLimit(rateLimit: WandaWebAclRateLimit) {
    const rules = (this.rules as CfnWebACL.RuleProperty[]) ?? [];
    const name = `basic-ratelimit`;

    rules.push({
      name,
      priority: Math.max(...rules.map(r => r.priority)) + 1,
      action: {
        block: {
          customResponse: {
            responseCode: 429,
            customResponseBodyKey: "tooManyRequests",
          },
        },
      },
      visibilityConfig: {
        sampledRequestsEnabled: true,
        cloudWatchMetricsEnabled: true,
        metricName: `${name}-metric`,
      },
      statement: {
        rateBasedStatement: {
          limit: rateLimit.limit * 60, // Convert to requests per minute
          aggregateKeyType: "IP", // Aggregate by IP address
          evaluationWindowSec: 60,
        },
      },
    });
    this.rules = rules;
  }

  /**
   * Associates the WebACL with an API Gateway deployment stage.
   * @param {string} resourceArn - The ARN of the resource to associate with the WebACL.
   * @returns {void}
   */
  associate(resourceArn: string) {
    new CfnWebACLAssociation(this, "WebACLAssociation", {
      resourceArn,
      webAclArn: this.attrArn,
    });
  }

  /**
   * Builds a managed rule for the WebACL.
   *
   * @param name - The name of the rule.
   * @param priority - The priority of the rule.
   * @param ruleGroup - The name of the AWS managed rule group.
   * @returns {CfnWebACL.RuleProperty} - The managed rule.
   */
  private static buildManagedRule(
    name: string,
    priority: number,
    ruleGroup: string,
    excludedRules?: string[]
  ): CfnWebACL.RuleProperty {
    const structuredExcludedRules = excludedRules?.map((rule) => ({
      name: rule,
    }));
    return {
      name,
      priority,
      statement: {
        managedRuleGroupStatement: {
          name: ruleGroup,
          vendorName: "AWS",
          excludedRules:
            excludedRules != null && excludedRules.length > 0
              ? structuredExcludedRules
              : undefined,
        },
      },
      visibilityConfig: {
        cloudWatchMetricsEnabled: true,
        metricName: `WandaWebACL-${name}`,
        sampledRequestsEnabled: true,
      },
      overrideAction: {
        none: {},
      },
    };
  }
}
