import {
  IResource,
  LambdaIntegration,
  MockIntegration,
  PassthroughBehavior,
  RestApi,
} from "aws-cdk-lib/aws-apigateway";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import { Construct } from "constructs";

interface WandaApiProps {
  getGameLambda: NodejsFunction;
  submitTravelPlansLambda: NodejsFunction;
  initiatePrizeDrawPlayLambda: NodejsFunction;
  getPrizeDrawPlayResultLambda: NodejsFunction;
  getTravelPlanQuestionsForFormLambda: NodejsFunction;
}
export class WandaApi extends RestApi {
  constructor(scope: Construct, id: string, props: WandaApiProps) {
    const {
      getGameLambda,
      submitTravelPlansLambda,
      initiatePrizeDrawPlayLambda,
      getPrizeDrawPlayResultLambda,
      getTravelPlanQuestionsForFormLambda,
    } = props;
    super(scope, id, {
      restApiName: id,
    });

    const getGameLambdaIntegration = new LambdaIntegration(getGameLambda);
    const submitTravelPlansLambdaIntegration = new LambdaIntegration(
      submitTravelPlansLambda,
    );
    const initiatePrizeDrawPlayLambdaIntegration = new LambdaIntegration(
      initiatePrizeDrawPlayLambda,
    );
    const getPrizeDrawPlayResultLambdaIntegration = new LambdaIntegration(
      getPrizeDrawPlayResultLambda,
    );
    const getTravelPlanQuestionsForFormLambdaIntegration = new LambdaIntegration(
      getTravelPlanQuestionsForFormLambda,
    );

    const games = this.root.addResource("games");
    games.addMethod("GET", getGameLambdaIntegration);
    addCorsOptions(games);

    const getPrizeDrawPlayResult = this.root.addResource(
      "getPrizeDrawPlayResult",
    );
    getPrizeDrawPlayResult.addMethod(
      "GET",
      getPrizeDrawPlayResultLambdaIntegration,
    );
    addCorsOptions(getPrizeDrawPlayResult);

    const submitTravelPlans = this.root.addResource("submitTravelPlans");
    submitTravelPlans.addMethod("POST", submitTravelPlansLambdaIntegration);
    addCorsOptions(submitTravelPlans);

    const initiatePrizeDrawPlay = this.root.addResource(
      "initiatePrizeDrawPlay",
    );
    initiatePrizeDrawPlay.addMethod(
      "POST",
      initiatePrizeDrawPlayLambdaIntegration,
    );
    addCorsOptions(initiatePrizeDrawPlay);

    const getTravelPlanQuestionsForForm = this.root.addResource(
      "getTravelPlanQuestionsForForm",
    );
    getTravelPlanQuestionsForForm.addMethod(
      "GET",
      getTravelPlanQuestionsForFormLambdaIntegration,
    );
    addCorsOptions(getTravelPlanQuestionsForForm);
  }
}

export function addCorsOptions(apiResource: IResource) {
  apiResource.addMethod(
    "OPTIONS",
    new MockIntegration({
      // In case you want to use binary media types, uncomment the following line
      // contentHandling: ContentHandling.CONVERT_TO_TEXT,
      integrationResponses: [
        {
          statusCode: "200",
          responseParameters: {
            "method.response.header.Access-Control-Allow-Headers":
              "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent'",
            "method.response.header.Access-Control-Allow-Origin": "'*'",
            "method.response.header.Access-Control-Allow-Credentials":
              "'false'",
            "method.response.header.Access-Control-Allow-Methods":
              "'OPTIONS,GET,PUT,POST,DELETE'",
          },
        },
      ],
      // In case you want to use binary media types, comment out the following line
      passthroughBehavior: PassthroughBehavior.NEVER,
      requestTemplates: {
        "application/json": '{"statusCode": 200}',
      },
    }),
    {
      methodResponses: [
        {
          statusCode: "200",
          responseParameters: {
            "method.response.header.Access-Control-Allow-Headers": true,
            "method.response.header.Access-Control-Allow-Methods": true,
            "method.response.header.Access-Control-Allow-Credentials": true,
            "method.response.header.Access-Control-Allow-Origin": true,
          },
        },
      ],
    },
  );
}
