import { Construct } from "constructs";
import { Queue } from "aws-cdk-lib/aws-sqs";
import { getStage } from "../../helpers/getStage";
import { BaseSQS } from "./baseSqs";

export type RetrievePrizeFromDrawQueueMessage = {
  userId: string;
  drawPlayId: string;
  drawId: string;
};

export class RetrievePrizeFromDrawSQSQueue {
  public queue: Queue;
  public dlq: Queue;

  constructor(scope: Construct) {
    const { stage } = getStage(scope);
    const id = `${stage}-retrieve-prize-from-draw-queue`;
    const queues = new BaseSQS(scope, id, {
      fifoQueue: true,
      contentBasedDeduplication: true,
    });
    this.queue = queues.queue;
    this.dlq = queues.dlq;
  }
}
