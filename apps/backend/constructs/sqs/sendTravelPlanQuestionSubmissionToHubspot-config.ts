import { Construct } from "constructs";
import { Queue } from "aws-cdk-lib/aws-sqs";
import { getStage } from "../../helpers/getStage";
import { BaseSQS } from "./baseSqs";
import { DynamoDBRecord } from "aws-lambda";

export type SendTravelPlanQuestionSubmissionToHubspotQueueMessage = {
  record: DynamoDBRecord;
};

export class SendTravelPlanQuestionSubmissionToHubspotSQSQueue {
  public queue: Queue;
  public dlq: Queue;

  constructor(scope: Construct) {
    const { stage } = getStage(scope);
    const id = `${stage}TravelPlanQuestionSubmissionQueue`;
    const queues = new BaseSQS(scope, id, {
      fifoQueue: false,
    });
    this.queue = queues.queue;
    this.dlq = queues.dlq;
  }
}
