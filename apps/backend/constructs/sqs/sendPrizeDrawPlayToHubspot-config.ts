import { Construct } from "constructs";
import { DynamoDBRecord } from "aws-lambda";
import { Queue } from "aws-cdk-lib/aws-sqs";
import { getStage } from "../../helpers/getStage";
import { BaseSQS } from "./baseSqs";

export type SendPrizeDrawPlayToHubspotQueueMessage = {
  record: DynamoDBRecord;
};

export class SendPrizeDrawPlayToHubspotSQSQueue {
  public queue: Queue;
  public dlq: Queue;

  constructor(scope: Construct) {
    const { stage } = getStage(scope);
    const id = `${stage}PrizeDrawPlayQueue`;
    const queues = new BaseSQS(scope, id, {
      fifoQueue: false,
    });
    this.queue = queues.queue;
    this.dlq = queues.dlq;
  }
}
