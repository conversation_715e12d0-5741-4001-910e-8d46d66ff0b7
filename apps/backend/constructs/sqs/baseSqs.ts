import { Construct } from "constructs";
import { Queue, QueueProps } from "aws-cdk-lib/aws-sqs";
import { Duration } from "aws-cdk-lib";

export interface BaseSQSProps {
  extraQueueProps?: QueueProps;
  extraDlqProps?: QueueProps;
  fifoQueue?: boolean;
  contentBasedDeduplication?: boolean;
}

export class BaseSQS extends Construct {
  public queue: Queue;
  public dlq: Queue;

  constructor(scope: Construct, id: string, props: BaseSQSProps = {}) {
    super(scope, id);

    const fifoSuffix = props.fifoQueue === true ? ".fifo" : "";

    const dlqProps: QueueProps = {
      queueName: `${id}-DLQ${fifoSuffix}`,
      retentionPeriod: Duration.days(14),
      visibilityTimeout: Duration.seconds(30),
      fifo: props.fifoQueue,
      contentBasedDeduplication: props.contentBasedDeduplication,
      ...props.extraDlqProps,
    };

    this.dlq = new Queue(this, `${id}-DLQ`, dlqProps);

    const defaultProps: QueueProps = {
      queueName: `${id}${fifoSuffix}`,
      retentionPeriod: Duration.days(4),
      visibilityTimeout: Duration.seconds(30),
      deadLetterQueue: {
        queue: this.dlq,
        maxReceiveCount: 5,
      },
      fifo: props.fifoQueue,
      contentBasedDeduplication: props.contentBasedDeduplication,
      ...props.extraQueueProps,
    };

    this.queue = new Queue(this, id, defaultProps);
  }
}
