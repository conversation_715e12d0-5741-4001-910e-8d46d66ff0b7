import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import { Construct } from "constructs";
import { Runtime } from "aws-cdk-lib/aws-lambda";
import {
  Effect,
  PolicyStatement,
  Role,
  ServicePrincipal,
} from "aws-cdk-lib/aws-iam";
import { Duration } from "aws-cdk-lib";
import { getStage } from "../../helpers/getStage";

export interface BaseLambdaProps {
  lambdaEntry: string;
  platformTableName?: string;
  environment?: Record<string, string>;
  memorySize?: number;
  timeout?: number;
  role?: Role;
  policyStatements?: PolicyStatement[];
}

export class BaseLambda extends NodejsFunction {
  constructor(scope: Construct, id: string, props: BaseLambdaProps) {
    const {
      lambdaEntry,
      environment,
      memorySize,
      timeout,
      policyStatements,
      platformTableName,
      role,
    } = props;
    const functionName = `${id}-lambda`;

    let roleToUse = role;
    if (!roleToUse) {
      const lambdaRole = new Role(scope, `${id}-lambda-role`, {
        assumedBy: new ServicePrincipal("lambda.amazonaws.com"),
        description: `Role for ${id} Lambda function`,
      });

      lambdaRole.addToPolicy(
        new PolicyStatement({
          resources: ["*"],
          actions: [
            "lambda:InvokeFunction",
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents",
          ],
          effect: Effect.ALLOW,
        }),
      );
      policyStatements?.forEach((statement) =>
        lambdaRole.addToPolicy(statement),
      );

      roleToUse = lambdaRole;
    }

    const { stage, isProd } = getStage(scope);

    super(scope, `${id}-lambda`, {
      functionName,
      runtime: Runtime.NODEJS_20_X,
      entry: lambdaEntry,
      memorySize: memorySize || 256,
      environment: {
        ...(platformTableName && { PLATFORM_TABLE: platformTableName }),
        ...environment,
        STAGE: isProd ? "prod" : stage,
      },
      role: roleToUse,
      timeout: timeout ? Duration.seconds(timeout) : Duration.seconds(5),
    });
  }
}
