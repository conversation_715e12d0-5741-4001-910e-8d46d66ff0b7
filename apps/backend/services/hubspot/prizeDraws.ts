import { Client } from "@hubspot/api-client";
import { getPrizeDrawObjectId } from "./customObjectIds";
import {
  BatchResponsePublicAssociationMulti,
  BatchResponsePublicAssociationMultiWithErrors,
} from "@hubspot/api-client/lib/codegen/crm/associations";

export interface HubspotPrizeDraw {
  id: string;
  properties: {
    prize_draw_reference: string;
    prize_slot_buffer: string;
  };
}

export const sendPrizeDrawToHubspot = async (
  hubspotClient: Client,
  prizeDrawId: string,
  prizeDrawName: string,
  prizeDrawStartDate: string,
  prizeDrawEndDate: string,
  prizeDrawSlotsBuffer: number,
): Promise<string> => {;
  const properties = {
    prize_draw_name: prizeDrawName,
    prize_draw_reference: prizeDrawId,
    start_date: prizeDrawStartDate,
    end_date: prizeDrawEndDate,
    prize_slot_buffer: prizeDrawSlotsBuffer.toString(),
  };

  const prizeDrawObjectId = getPrizeDrawObjectId();

  const response = await hubspotClient.crm.objects.basicApi.create(
    prizeDrawObjectId,
    {
      properties,
      associations: [],
    },
  );

  return response.id;
};

export const getAllPrizeDrawHubspotRecordIds = (
  associationsResponse:
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti,
): string[] => {
  return Array.from(
    new Set(
      associationsResponse.results
        .map((result) => result.to[0]?.id)
        .filter(Boolean),
    ),
  );
};