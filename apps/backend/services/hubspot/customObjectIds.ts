const STAGING = {
  CONTACTS_OBJECT_ID: "0-1",
  POLICY_DETAIL_OBJECT_ID: "2-*********",
  PRIZE_DRAW_OBJECT_ID: "2-*********",
  PRIZE_DRAW_CONFIG_OBJECT_ID: "2-*********",
  PRIZE_DRAW_PLAY_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_FORM_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_QUESTION_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_QUESTION_SUBMISSION_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_COMBOBOX_OBJECT_ID: "2-*********"
};

const PRODUCTION = {
  CONTACTS_OBJECT_ID: "0-1",
  POLICY_DETAIL_OBJECT_ID: "2-*********",
  PRIZE_DRAW_OBJECT_ID: "2-*********",
  PRIZE_DRAW_CONFIG_OBJECT_ID: "2-*********",
  PRIZE_DRAW_PLAY_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_FORM_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_QUESTION_SUBMISSION_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_QUESTION_OBJECT_ID: "2-*********",
  TRAVEL_PLAN_COMBOBOX_OBJECT_ID: "2-*********"
};

const stage = process.env.STAGE_NAME || 'staging';
const isProd = stage === 'prod';
const CONFIG = isProd ? PRODUCTION : STAGING;

export const getContactsObjectId = () => CONFIG.CONTACTS_OBJECT_ID;
export const getPolicyDetailObjectId = () => CONFIG.POLICY_DETAIL_OBJECT_ID;
export const getPrizeDrawObjectId = () => CONFIG.PRIZE_DRAW_OBJECT_ID;
export const getPrizeDrawConfigObjectId = () => CONFIG.PRIZE_DRAW_CONFIG_OBJECT_ID;
export const getPrizeDrawPlayObjectId = () => CONFIG.PRIZE_DRAW_PLAY_OBJECT_ID;
export const getTravelPlanQuestionSubmissionObjectId = () => CONFIG.TRAVEL_PLAN_QUESTION_SUBMISSION_OBJECT_ID;
export const getTravelPlanFormObjectId = () => CONFIG.TRAVEL_PLAN_FORM_OBJECT_ID;
export const getTravelPlanQuestionObjectId = () => CONFIG.TRAVEL_PLAN_QUESTION_OBJECT_ID;
export const getTravelPlanComboboxObjectId = () => CONFIG.TRAVEL_PLAN_COMBOBOX_OBJECT_ID;
