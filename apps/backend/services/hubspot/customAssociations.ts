const STAGING = {
  PRIZE_DRAW_CONFIG_TO_PRIZE_DRAW: 127,
  PRIZE_DRAW_PLAY_TO_PRIZE_DRAW: 79,
  PRIZE_DRAW_PLAY_TO_CONTACT: 83,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_CONTACT: 219,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_TRAVEL_PLAN_FORM: 208,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_TRAVEL_PLAN_QUESTION: 216,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_PRIZE_DRAW: 228,
};

const PRODUCTION = {
  PRIZE_DRAW_CONFIG_TO_PRIZE_DRAW: 93,
  PRIZE_DRAW_PLAY_TO_PRIZE_DRAW: 117,
  PRIZE_DRAW_PLAY_TO_CONTACT: 113,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_CONTACT: 247,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_PRIZE_DRAW: 251,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_TRAVEL_PLAN_FORM: 226,
  TRAVEL_PLAN_QUESTION_SUBMISSION_TO_TRAVEL_PLAN_QUESTION: 236,
};

const stage = process.env.STAGE_NAME || 'staging';
const isProd = stage === 'prod';
const CONFIG = isProd ? PRODUCTION : STAGING;

export const getPrizeDrawConfigToPrizeDrawAssociationTypeId = () => CONFIG.PRIZE_DRAW_CONFIG_TO_PRIZE_DRAW;
export const getPrizeDrawPlayToPrizeDrawAssociationTypeId = () => CONFIG.PRIZE_DRAW_PLAY_TO_PRIZE_DRAW;
export const getPrizeDrawPlayToContactAssociationTypeId = () => CONFIG.PRIZE_DRAW_PLAY_TO_CONTACT;
export const getTravelPlanQuestionSubmissionToContactAssociationTypeId = () => CONFIG.TRAVEL_PLAN_QUESTION_SUBMISSION_TO_CONTACT;
export const getTravelPlanQuestionSubmissionToTravelPlanFormAssociationTypeId = () => CONFIG.TRAVEL_PLAN_QUESTION_SUBMISSION_TO_TRAVEL_PLAN_FORM;
export const getTravelPlanQuestionSubmissionToTravelPlanQuestionAssociationTypeId = () => CONFIG.TRAVEL_PLAN_QUESTION_SUBMISSION_TO_TRAVEL_PLAN_QUESTION;
export const getTravelPlanQuestionSubmissionToPrizeDrawAssociationTypeId = () => CONFIG.TRAVEL_PLAN_QUESTION_SUBMISSION_TO_PRIZE_DRAW;