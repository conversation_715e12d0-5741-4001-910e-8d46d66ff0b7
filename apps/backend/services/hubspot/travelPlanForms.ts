import { Client } from '@hubspot/api-client';
import { FilterOperatorEnum, PublicObjectSearchRequest, SimplePublicObject } from '@hubspot/api-client/lib/codegen/crm/companies';
import { getTravelPlanFormObjectId } from './customObjectIds';

export interface HubspotTravelPlanForm {
  form_name: string;
  title: string;
  travel_plan_form_cuid: string;
  weighted_probability: string;
  hs_createdate: string;
  hs_modifieddate: string;
  hs_object_id: string;
}

export const fetchTravelPlanFormsFromHubspot = async(
  hubspotClient: Client,
  lastSyncDate: string | undefined
): Promise<{ results: HubspotTravelPlanForm[] }> => {
  const travelPlanFormObjectId = getTravelPlanFormObjectId();
  let allTravelPlanForms: SimplePublicObject[] = [];
  let after: string | undefined = undefined;

  const filterGroups = [];
  
  if (lastSyncDate) {
    filterGroups.push(
      {
        filters: [{
          propertyName: "hs_lastmodifieddate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      },
      {
        filters: [{
          propertyName: "hs_createdate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]  
      }
    );
  }

  do {
    const searchRequest: PublicObjectSearchRequest = {
      filterGroups,
      properties: [
        "travel_plan_form_cuid",
        "weighted_probability",
        "hs_createdate",
        "hs_modifieddate",
        "form_name",
        "title",
      ],
      limit: 100,
      after,
    };

    const response = await hubspotClient.crm.objects.searchApi.doSearch(
      travelPlanFormObjectId,
      searchRequest,
    );

    allTravelPlanForms = [...allTravelPlanForms, ...response.results];

    after = response.paging?.next?.after ?? undefined;
  } while (after);

  const typedTravelPlanForms: HubspotTravelPlanForm[] = allTravelPlanForms.map((form) => form.properties as unknown as HubspotTravelPlanForm);

  return { results: typedTravelPlanForms};
};

export const updateHubspotTravelPlanFormsWithUuid = async(hubspotClient: Client, newForms: HubspotTravelPlanForm[]) => {
  if (newForms.length === 0) return;
    const travelPlanFormObjectId = getTravelPlanFormObjectId();
  
    const updateObjects = newForms.map(form => ({
      id: form.hs_object_id,
      properties: {
        travel_plan_form_cuid: form.travel_plan_form_cuid
      }
    }));
  
    await hubspotClient.crm.objects.batchApi.update(travelPlanFormObjectId, { inputs: updateObjects });
}

export const fetchDeletedTravelPlanForms = async (
  hubspotClient: Client,
  lastSyncDate: string | undefined,
): Promise<(string | null)[]> => {
  const travelPlanFormObjectId = getTravelPlanFormObjectId();
  try {
    const response = await hubspotClient.crm.objects.basicApi.getPage(
      travelPlanFormObjectId,
      100,
      undefined,
      ["travel_plan_form_cuid", "hs_lastmodifieddate"],
      undefined,
      undefined,
      true, // archived items only
    );
    return response.results
      .filter(
        (result) =>
          !lastSyncDate ||
          (result.archivedAt &&
            new Date(result.archivedAt) >= new Date(lastSyncDate)),
      )
      .map((result) => result.properties.travel_plan_form_cuid);
  } catch (error) {
    console.error("Error fetching deleted travel plan forms:", error);
    return [];
  }
};