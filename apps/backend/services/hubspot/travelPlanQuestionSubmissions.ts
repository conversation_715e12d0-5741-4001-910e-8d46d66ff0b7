import { Client } from "@hubspot/api-client";
import { getContactsObjectId, getPrizeDrawObjectId, getTravelPlanFormObjectId, getTravelPlanQuestionObjectId, getTravelPlanQuestionSubmissionObjectId } from "./customObjectIds";
import { getTravelPlanQuestionSubmissionToContactAssociationTypeId, getTravelPlanQuestionSubmissionToPrizeDrawAssociationTypeId, getTravelPlanQuestionSubmissionToTravelPlanFormAssociationTypeId, getTravelPlanQuestionSubmissionToTravelPlanQuestionAssociationTypeId } from "./customAssociations";


export const sendTravelPlanQuestionSubmissionToHubspot = async ({
  hubspotClient, 
  answer, 
  hubspotPrizeDrawId,
  hubspotUserId,
  hubspotFormId,
  hubspotQuestionId,
  questionSubmissionId,
}: {
  hubspotClient: Client, 
  answer: string, 
  hubspotPrizeDrawId: string,
  hubspotUserId: string,
  hubspotFormId: string,
  hubspotQuestionId: string,
  questionSubmissionId: string,
}): Promise<string> => {

  const contactsObjectId = getContactsObjectId();
  const travelPlanFormObjectId = getTravelPlanFormObjectId();
  const prizeDrawObjectId = getPrizeDrawObjectId();
  const travelPlanQuestionSubmissionObjectId = getTravelPlanQuestionSubmissionObjectId();
  const travelPlanQuestionObjectId = getTravelPlanQuestionObjectId();

  const travelPlanQuestionSubmissionToContactAssociationTypeId = getTravelPlanQuestionSubmissionToContactAssociationTypeId();
  const travelPlanQuestionSubmissionToTravelPlanFormAssociationTypeId = getTravelPlanQuestionSubmissionToTravelPlanFormAssociationTypeId();
  const travelPlanQuestionSubmissionToTravelPlanQuestionAssociationTypeId = getTravelPlanQuestionSubmissionToTravelPlanQuestionAssociationTypeId();
  const travelPlanQuestionSubmissionToPrizeDrawAssociationTypeId = getTravelPlanQuestionSubmissionToPrizeDrawAssociationTypeId();

  const travelPlanQuestionSubmissionInput = {
    properties: {
      answer,
      cuid: questionSubmissionId,
    },
  };
  console.log("Creating TravelPlan Submission: ", "questionSubmissionId", questionSubmissionId);
  const response = await hubspotClient.crm.objects.basicApi.create(
    travelPlanQuestionSubmissionObjectId,
    travelPlanQuestionSubmissionInput,
  );

  console.log(`Associating (${travelPlanQuestionSubmissionToContactAssociationTypeId}) TravelPlan Submission (${response.id}, ${travelPlanQuestionSubmissionObjectId}) to Contact (${hubspotUserId}, ${contactsObjectId})`);
  const travelPlanQuestionSubmissionToContactAssociationSpec = [
    {
      associationCategory: 'USER_DEFINED',
      associationTypeId: travelPlanQuestionSubmissionToContactAssociationTypeId,
    },
  ];
  await hubspotClient.crm.associations.v4.basicApi.create(
    travelPlanQuestionSubmissionObjectId,
    response.id,
    contactsObjectId,
    hubspotUserId,
    travelPlanQuestionSubmissionToContactAssociationSpec
  );
  console.log(`Associating (${travelPlanQuestionSubmissionToTravelPlanFormAssociationTypeId}) TravelPlan Submission (${response.id}, ${travelPlanQuestionSubmissionObjectId}) to TravelPlan Form (${hubspotFormId}, ${travelPlanFormObjectId})`);
  const travelPlanQuestionSubmissionToTravelPlanFormAssociationSpec = [
    {
      associationCategory: 'USER_DEFINED',
      associationTypeId: travelPlanQuestionSubmissionToTravelPlanFormAssociationTypeId,
    },
  ];
  await hubspotClient.crm.associations.v4.basicApi.create(
    travelPlanQuestionSubmissionObjectId,
    response.id,
    travelPlanFormObjectId,
    hubspotFormId,
    travelPlanQuestionSubmissionToTravelPlanFormAssociationSpec
  );
  console.log(`Associating (${travelPlanQuestionSubmissionToTravelPlanQuestionAssociationTypeId}) TravelPlan Submission (${response.id}, ${travelPlanQuestionSubmissionObjectId}) to TravelPlan Question (${hubspotQuestionId}, ${travelPlanQuestionObjectId})`);
  const travelPlanQuestionSubmissionToTravelPlanQuestionAssociationSpec = [
    {
      associationCategory: 'USER_DEFINED',
      associationTypeId: travelPlanQuestionSubmissionToTravelPlanQuestionAssociationTypeId,
    },
  ];
  await hubspotClient.crm.associations.v4.basicApi.create(
    travelPlanQuestionSubmissionObjectId,
    response.id,
    travelPlanQuestionObjectId,
    hubspotQuestionId,
    travelPlanQuestionSubmissionToTravelPlanQuestionAssociationSpec
  );
  console.log(`Associating (${travelPlanQuestionSubmissionToPrizeDrawAssociationTypeId}) TravelPlan Submission (${response.id}, ${travelPlanQuestionSubmissionObjectId}) to PrizeDraw (${hubspotPrizeDrawId}, ${prizeDrawObjectId})`);
  const travelPlanQuestionSubmissionToPrizeDrawAssociationSpec = [
    {
      associationCategory: 'USER_DEFINED',
      associationTypeId: travelPlanQuestionSubmissionToPrizeDrawAssociationTypeId,
    },
  ];
  await hubspotClient.crm.associations.v4.basicApi.create(
    travelPlanQuestionSubmissionObjectId,
    response.id,
    prizeDrawObjectId,
    hubspotPrizeDrawId,
    travelPlanQuestionSubmissionToPrizeDrawAssociationSpec
  );

  return response.id;
}