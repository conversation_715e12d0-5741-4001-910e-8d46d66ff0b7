import { Client } from '@hubspot/api-client';
import { getEnvVariable } from "../../helpers/getEnvVariable";
import { getSecret } from "../../lambdas/helpers";
import { BatchResponsePublicAssociationMulti, BatchResponsePublicAssociationMultiStatusEnum, BatchResponsePublicAssociationMultiWithErrors } from '@hubspot/api-client/lib/codegen/crm/associations';
import { SimplePublicObject } from '@hubspot/api-client/lib/codegen/crm/companies';
import { HubspotTravelPlanQuestion } from './travelPlanQuestions';

export const getHubspotApiToken = async (): Promise<string> => {
  return await getSecret(getEnvVariable("HUBSPOT_API_TOKEN_SECRET_NAME"));
};

export const fetchAssociatedObjects = async (
  hubspotClient: Client,
  objectIds: string[],
  fromObjectId: string,
  toObjectId: string,
): Promise<
  | BatchResponsePublicAssociationMultiWithErrors
  | BatchResponsePublicAssociationMulti
> => {
  const batchSize = 100;
  const results: (
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti
  )[] = [];

  // Process in batches of 100
  for (let i = 0; i < objectIds.length; i += batchSize) {
    const batch = objectIds.slice(i, i + batchSize);
    const batchResult = await hubspotClient.crm.associations.batchApi.read(
      fromObjectId,
      toObjectId,
      { inputs: batch.map((id) => ({ id })) },
    );
    results.push(batchResult);
  }

  return {
    status: BatchResponsePublicAssociationMultiStatusEnum.Complete,
    results: results.flatMap((result) => result.results),
    startedAt: results[0]?.startedAt,
    completedAt: results[results.length - 1]?.completedAt,
  };
};

export const getAllObjectIds = (
  associationsResponse:
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti,
): string[] => {
  return Array.from(
    new Set(
      associationsResponse.results.flatMap((result) =>
        result.to.map((t) => t.id),
      ),
    ),
  );
};

export const fetchObjectDetails = async <T>(
  hubspotClient: Client,
  objectIds: string[],
  hubspotObjectId: string,
  properties: string[],
): Promise<Map<string, T>> => {
  if (objectIds.length === 0) return new Map();

  const batchSize = 100;
  const results: SimplePublicObject[] = [];

  // Process in batches of 100
  for (let i = 0; i < objectIds.length; i += batchSize) {
    const batch = objectIds.slice(i, i + batchSize);
    const batchResponse = await hubspotClient.crm.objects.batchApi.read(
      hubspotObjectId,
      {
        properties,
        propertiesWithHistory: [],
        inputs: batch.map((id) => ({ id: id.toString() })),
      },
    );
    results.push(...batchResponse.results);
  }

  return new Map(
    results.map((object) => [
      object.id,
      object as unknown as T,
    ]),
  );
};

export const createObjectToAssociationsMap = (
  associationsResponse:
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti,
): Map<string, string[]> => {
  return new Map(
    associationsResponse.results.map((result) => [
      result._from.id,
      result.to.map((t) => t.id),
    ]),
  );
};