import { Client } from '@hubspot/api-client';
import { FilterOperatorEnum, PublicObjectSearchRequest, SimplePublicObject } from '@hubspot/api-client/lib/codegen/crm/companies';
import { getTravelPlanQuestionObjectId } from './customObjectIds';

export interface HubspotTravelPlanQuestion {
  travel_plan_question_cuid: string;
  question_name: string;
  question_text: string;
  question_format: string;
  question_placeholder_text: string;
  hs_createdate: string;
  hs_modifieddate: string;
  hs_object_id: string;
  travel_plan_form_cuid?: string;
}

export const fetchTravelPlanQuestionsFromHubspot = async(
  hubspotClient: Client,
  lastSyncDate: string | undefined
): Promise<{ results: HubspotTravelPlanQuestion[] }> => {
  const travelPlanQuestionObjectId = getTravelPlanQuestionObjectId();
  let allTravelPlanQuestions: SimplePublicObject[] = [];
  let after: string | undefined = undefined;

  const filterGroups = [];

  if (lastSyncDate) {
    filterGroups.push(
      {
        filters: [{
          propertyName: "hs_lastmodifieddate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      },
      {
        filters: [{
          propertyName: "hs_createdate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      }
    );
  }

  do {
    const searchRequest: PublicObjectSearchRequest = {
      filterGroups,
      properties: [
        "travel_plan_question_cuid",
        "question_name",
        "question_text",
        "question_format",
        "question_placeholder_text",
        "hs_createdate",
        "hs_modifieddate",
      ],
      limit: 100,
      after,
    };

    const response = await hubspotClient.crm.objects.searchApi.doSearch(
      travelPlanQuestionObjectId,
      searchRequest,
    );

    allTravelPlanQuestions = [...allTravelPlanQuestions, ...response.results];

    after = response.paging?.next?.after ?? undefined;
  } while (after);

  const typedTravelPlanQuestions: HubspotTravelPlanQuestion[] = allTravelPlanQuestions.map((question) => question.properties as unknown as HubspotTravelPlanQuestion);

  return { results: typedTravelPlanQuestions};
};

export const updateHubspotTravelPlanQuestionsWithUuid = async(hubspotClient: Client, newQuestions: HubspotTravelPlanQuestion[]) => {
  if (newQuestions.length === 0) return;
  const travelPlanQuestionObjectId = getTravelPlanQuestionObjectId();
  
  // Process in batches of 100
  for (let i = 0; i < newQuestions.length; i += 100) {
    const batch = newQuestions.slice(i, i + 100);
    const updateObjects = batch.map(question => ({
      id: question.hs_object_id,
      properties: {
        travel_plan_question_cuid: question.travel_plan_question_cuid
      }
    }));
    
    await hubspotClient.crm.objects.batchApi.update(travelPlanQuestionObjectId, { inputs: updateObjects });
  }
}

export const fetchDeletedTravelPlanQuestions = async (
  hubspotClient: Client,
  lastSyncDate: string | undefined,
): Promise<(string | null)[]> => {
  const travelPlanQuestionObjectId = getTravelPlanQuestionObjectId();
  try {
    const response = await hubspotClient.crm.objects.basicApi.getPage(
      travelPlanQuestionObjectId,
      100,
      undefined,
      ["travel_plan_question_cuid", "hs_lastmodifieddate"],
      undefined,
      undefined,
      true, // archived items only
    );
    return response.results
      .filter(
        (result) =>
          !lastSyncDate ||
          (result.archivedAt &&
            new Date(result.archivedAt) >= new Date(lastSyncDate)),
      )
      .map((result) => result.properties.travel_plan_question_cuid);
  } catch (error) {
    console.error("Error fetching deleted travel plan questions:", error);
    return [];
  }
};

export const combineTravelPlanQuestionWithAssoicatedTravelPlanFormCuid = (
  objects: HubspotTravelPlanQuestion[],
  associationsMap: Map<string, string[]>,
  associatedObjectsMap: Map<string, unknown>,
): HubspotTravelPlanQuestion[] => {
  return objects.map((object) => {
    const objectAssociations = associationsMap.get(object.hs_object_id) || [];
    const associatedObjects = objectAssociations
      .map((assocId) => associatedObjectsMap.get(assocId))
      .filter((object): object is SimplePublicObject => object !== undefined);

    return {
      ...object,
      travel_plan_form_cuid: associatedObjects[0].properties.travel_plan_form_cuid,
    } as unknown as HubspotTravelPlanQuestion;
  });
};