import { Client } from '@hubspot/api-client';
import { FilterOperatorEnum, PublicObjectSearchRequest, SimplePublicObject } from '@hubspot/api-client/lib/codegen/crm/companies';
import { getTravelPlanComboboxObjectId } from './customObjectIds';

export interface HubspotTravelPlanCombobox {
  travel_plan_multibox_cuid: string;
  combobox_option_name: string;
  option_text: string;
  hs_createdate: string;
  hs_modifieddate: string;
  hs_object_id: string;
  travel_plan_question_cuid?: string;
}

export const fetchTravelPlanComboboxesFromHubspot = async(
  hubspotClient: Client,
  lastSyncDate: string | undefined
): Promise<{ results: HubspotTravelPlanCombobox[] }> => {
  const travelPlanComboboxObjectId = getTravelPlanComboboxObjectId();
  let allTravelPlanComboboxes: SimplePublicObject[] = [];
  let after: string | undefined = undefined;

  const filterGroups = [];

  if (lastSyncDate) {
    filterGroups.push(
      {
        filters: [{
          propertyName: "hs_lastmodifieddate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      },
      {
        filters: [{
          propertyName: "hs_createdate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      }
    );
  }

  do {
    const searchRequest: PublicObjectSearchRequest = {
      filterGroups,
      properties: [
        "travel_plan_multibox_cuid",
        "combobox_option_name",
        "option_text",
        "hs_createdate",
        "hs_modifieddate",
      ],
      limit: 100,
      after,
    };

    const response = await hubspotClient.crm.objects.searchApi.doSearch(
      travelPlanComboboxObjectId,
      searchRequest,
    );

    allTravelPlanComboboxes = [...allTravelPlanComboboxes, ...response.results];

    after = response.paging?.next?.after ?? undefined;
  } while (after);

  const typedTravelPlanComboboxes: HubspotTravelPlanCombobox[] = allTravelPlanComboboxes.map((combobox) => combobox.properties as unknown as HubspotTravelPlanCombobox);

  return { results: typedTravelPlanComboboxes};
};

export const updateHubspotTravelPlanComboboxesWithUuid = async(hubspotClient: Client, newComboboxes: HubspotTravelPlanCombobox[]) => {
  if (newComboboxes.length === 0) return;
  const travelPlanComboboxObjectId = getTravelPlanComboboxObjectId();
  
  // Process in batches of 100
  for (let i = 0; i < newComboboxes.length; i += 100) {
    const batch = newComboboxes.slice(i, i + 100);
    const updateObjects = batch.map(combobox => ({
      id: combobox.hs_object_id,
      properties: {
        travel_plan_multibox_cuid: combobox.travel_plan_multibox_cuid
      }
    }));
    
    await hubspotClient.crm.objects.batchApi.update(travelPlanComboboxObjectId, { inputs: updateObjects });
  }
}

export const fetchDeletedTravelPlanComboboxes = async (
  hubspotClient: Client,
  lastSyncDate: string | undefined,
): Promise<(string | null)[]> => {
  const travelPlanComboboxObjectId = getTravelPlanComboboxObjectId();
  try {
    const response = await hubspotClient.crm.objects.basicApi.getPage(
      travelPlanComboboxObjectId,
      100,
      undefined,
      ["travel_plan_multibox_cuid", "hs_lastmodifieddate"],
      undefined,
      undefined,
      true, // archived items only
    );
    return response.results
      .filter(
        (result) =>
          !lastSyncDate ||
          (result.archivedAt &&
            new Date(result.archivedAt) >= new Date(lastSyncDate)),
      )
      .map((result) => result.properties.travel_plan_multibox_cuid);
  } catch (error) {
    console.error("Error fetching deleted travel plan comboboxes:", error);
    return [];
  }
};

export const combineTravelPlanComboboxWithAssoicatedTravelPlanFormCuid = (
  objects: HubspotTravelPlanCombobox[],
  associationsMap: Map<string, string[]>,
  associatedObjectsMap: Map<string, unknown>,
): HubspotTravelPlanCombobox[] => {
  return objects.map((object) => {
    const objectAssociations = associationsMap.get(object.hs_object_id) || [];
    const associatedObjects = objectAssociations
      .map((assocId) => associatedObjectsMap.get(assocId))
      .filter((object): object is SimplePublicObject => object !== undefined);

    return {
      ...object,
      travel_plan_question_cuid: associatedObjects[0].properties.travel_plan_question_cuid,
    } as unknown as HubspotTravelPlanCombobox;
  });
};