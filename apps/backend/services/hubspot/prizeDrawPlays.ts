import { Client } from "@hubspot/api-client";
import { getContactsObjectId, getPrizeDrawObjectId, getPrizeDrawPlayObjectId } from "./customObjectIds";
import { getPrizeDrawPlayToContactAssociationTypeId, getPrizeDrawPlayToPrizeDrawAssociationTypeId } from "./customAssociations";

export const sendPrizeDrawPlayToHubspot = async (
  hubspotClient: Client,
  pointsWon: string,
  drawPrizePlayId: string,
  hubspotDrawId: string,
  hubspotUserId: string,
  timeStamp: string,
): Promise<void> => {
  const prizeDrawObjectId = getPrizeDrawObjectId();
  const contactsObjectId = getContactsObjectId();
  const prizeDrawPlayObjectId = getPrizeDrawPlayObjectId();
  const prizeDrawPlayToPrizeDrawAssociationTypeId = getPrizeDrawPlayToPrizeDrawAssociationTypeId();
  const prizeDrawPlayToContactAssociationTypeId = getPrizeDrawPlayToContactAssociationTypeId();
  const prizeDrawPlayInput = {
    properties: {
      points_won: pointsWon,
      play_timestamp: timeStamp,
      play_reference: drawPrizePlayId,
    },
  };
  console.log("Creating Prizedraw Play: ", "prizeDrawPlayObjectId", prizeDrawPlayObjectId, "prizeDrawInput", JSON.stringify(prizeDrawPlayInput));
  const response = await hubspotClient.crm.objects.basicApi.create(
    prizeDrawPlayObjectId,
    prizeDrawPlayInput,
  );
  console.log(`Associating (${prizeDrawPlayToPrizeDrawAssociationTypeId}) Prizedraw Play (${response.id}, ${prizeDrawPlayObjectId}) to Prizedraw (${hubspotDrawId}, ${prizeDrawObjectId})`);
  const prizeDrawPlayToPrizeDrawAssociationSpec = [
    {
      associationCategory: 'USER_DEFINED',
      associationTypeId: prizeDrawPlayToPrizeDrawAssociationTypeId,
    },
  ];
  await hubspotClient.crm.associations.v4.basicApi.create(
    prizeDrawPlayObjectId,
    response.id,
    prizeDrawObjectId,
    hubspotDrawId,
    prizeDrawPlayToPrizeDrawAssociationSpec
  );
  console.log(`Associating (${prizeDrawPlayToContactAssociationTypeId}) Prizedraw Play (${response.id}, ${prizeDrawPlayObjectId}) to Contact (${hubspotUserId}, ${contactsObjectId})`);
  const prizeDrawPlayToContactAssociationSpec = [
    {
      associationCategory: 'USER_DEFINED',
      associationTypeId: prizeDrawPlayToContactAssociationTypeId,
    },
  ];
  await hubspotClient.crm.associations.v4.basicApi.create(
    prizeDrawPlayObjectId,
    response.id,
    contactsObjectId,
    hubspotUserId,
    prizeDrawPlayToContactAssociationSpec
  );
  
};
