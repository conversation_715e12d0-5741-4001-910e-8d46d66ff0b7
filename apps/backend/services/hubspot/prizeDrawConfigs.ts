import { Client } from "@hubspot/api-client";
import {
  FilterOperatorEnum,
  PublicObjectSearchRequest,
  SimplePublicObject,
} from "@hubspot/api-client/lib/codegen/crm/contacts";
import {
  BatchResponsePublicAssociationMulti,
  BatchResponsePublicAssociationMultiWithErrors,
} from "@hubspot/api-client/lib/codegen/crm/associations";
import { AssociationSpecAssociationCategoryEnum } from "@hubspot/api-client/lib/codegen/crm/objects";
import { PrizeConfig } from "../../entities/prizeDrawConfig";
import { getPrizeDrawConfigObjectId, getPrizeDrawObjectId } from "./customObjectIds";
import { HubspotPrizeDraw } from "./prizeDraws";
import { getPrizeDrawConfigToPrizeDrawAssociationTypeId } from "./customAssociations";

export interface HubspotPrizeDrawConfigWithPrizeDraw {
  hs_record_id: string;
  hs_createdate: string;
  hs_modifieddate: string;
  prize_size: string;
  maximum_number_of_wins: string;
  guaranteed_prize: "true" | "false";
  cuid: string;
  prizeDraw: {
    prize_draw_reference: string;
    hs_createdate: string;
    hs_lastmodifieddate: string;
    hs_object_id: string;
  } | undefined;
}

export const batchInsertPrizeDrawConfigsToHubspot = async ({
  defaultPrizeConfigs,
  prizeDrawHubspotId,
  hubspotClient,
}: {
  defaultPrizeConfigs: PrizeConfig[];
  prizeDrawHubspotId: string;
  hubspotClient: Client;
}): Promise<{ hubspotRecordId: string; id: string | null }[]> => {
  const prizeDrawConfigObjectId = getPrizeDrawConfigObjectId();
  const prizeDrawConfigToPrizeDrawAssociationTypeId = getPrizeDrawConfigToPrizeDrawAssociationTypeId();
  const batchInput = {
    inputs: defaultPrizeConfigs.map((prizeConfig, index) => ({
      properties: {
        prize_size: prizeConfig.rewardAmount.toString(),
        maximum_number_of_wins: prizeConfig.maximumNumberOfWinners.toString(),
        guaranteed_prize:
          prizeConfig.prizeType === "GUARANTEED" ? "true" : "false",
        cuid: prizeConfig.configId,
        prize_configuration_reference: `Default Config ${index + 1}`
      },
      associations: [
        {
          to: {
            id: prizeDrawHubspotId,
            type: prizeDrawConfigObjectId,
          },
          types: [
            {
              associationCategory:
                AssociationSpecAssociationCategoryEnum.UserDefined,
              associationTypeId: prizeDrawConfigToPrizeDrawAssociationTypeId,
            },
          ],
        },
      ],
    })),
  };

  try {
    const response = await hubspotClient.crm.objects.batchApi.create(
      prizeDrawConfigObjectId,
      batchInput,
    );
    return response.results.map((result) => ({
      hubspotRecordId: result.id,
      id: result.properties.cuid,
    }));
  } catch (hubspotError) {
    console.error("Error creating HubSpot objects:", hubspotError);
    throw new Error("Error creating HubSpot objects");
  }
};

export const fetchPrizeDrawConfigs = async (
  hubspotClient: Client,
  lastSyncDate: string | undefined,
): Promise<{ results: SimplePublicObject[] }> => {
  const prizeDrawConfigObjectId = getPrizeDrawConfigObjectId();
  let allPrizeDrawConfigs: SimplePublicObject[] = [];
  let after: string | undefined = undefined;

  const filterGroups = [];

  if (lastSyncDate) {
    filterGroups.push(
      {
        filters: [{
          propertyName: "hs_lastmodifieddate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      },
      {
        filters: [{
          propertyName: "hs_createdate",
          operator: FilterOperatorEnum.Gte,
          value: lastSyncDate,
        }]
      }
    );
  }

  do {
    const searchRequest: PublicObjectSearchRequest = {
      filterGroups,
      properties: [
        "prize_size",
        "maximum_number_of_wins",
        "guaranteed_prize",
        "cuid",
        "prize_configuration_reference",
        "hs_createdate",
      ],
      limit: 100,
      after,
    };

    const response = await hubspotClient.crm.objects.searchApi.doSearch(
      prizeDrawConfigObjectId,
      searchRequest,
    );

    allPrizeDrawConfigs = [...allPrizeDrawConfigs, ...response.results];

    after = response.paging?.next?.after ?? undefined;
  } while (after);

  return { results: allPrizeDrawConfigs };
};

export const fetchAssociatedPrizeDraws = async (
  hubspotClient: Client,
  prizeDrawConfigIds: string[],
): Promise<
  | BatchResponsePublicAssociationMultiWithErrors
  | BatchResponsePublicAssociationMulti
> => {
  const prizeDrawConfigObjectId = getPrizeDrawConfigObjectId();
  const prizeDrawObjectId = getPrizeDrawObjectId();
  return await hubspotClient.crm.associations.batchApi.read(
    prizeDrawConfigObjectId,
    prizeDrawObjectId,
    { inputs: prizeDrawConfigIds.map((id) => ({ id })) },
  );
};

export const createPrizeDrawConfigToAssociationsMap = (
  associationsResponse:
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti,
): Map<string, string> => {
  return new Map(
    associationsResponse.results.map((result) => [
      result._from.id,
      result.to[0]?.id || "",
    ]),
  );
};



export const fetchPrizeDrawDetails = async (
  hubspotClient: Client,
  prizeDrawIds: string[],
): Promise<Map<string, HubspotPrizeDraw>> => {
  const prizeDrawObjectId = getPrizeDrawObjectId();
  if (prizeDrawIds.length === 0) return new Map();

  const batchResponse = await hubspotClient.crm.objects.batchApi.read(
    prizeDrawObjectId,
    {
      properties: ["prize_draw_reference", "prize_slot_buffer"],
      propertiesWithHistory: [],
      inputs: prizeDrawIds.map((id) => ({ id: id.toString() })),
    },
  );

  return new Map(
    batchResponse.results.map((prizeDraw) => [
      prizeDraw.id,
      prizeDraw as unknown as HubspotPrizeDraw,
    ]),
  );
};

export const combinePrizeDrawConfigsWithPrizeDraw = (
  prizeDrawConfigs: SimplePublicObject[],
  associationsMap: Map<string, string>,
  prizeDrawsMap: Map<string, HubspotPrizeDraw>,
): SimplePublicObject[] => {
  return prizeDrawConfigs.map((prizeDrawConfig) => {
    const prizeDrawId = associationsMap.get(prizeDrawConfig.id);
    const prizeDraw = prizeDrawId ? prizeDrawsMap.get(prizeDrawId) : undefined;

    return {
      ...prizeDrawConfig,
      properties: {
        ...prizeDrawConfig.properties,
        prizeDraw: prizeDraw?.properties,
        hs_record_id: prizeDrawConfig.id,
      },
    } as unknown as SimplePublicObject;
  });
};

export const fetchDeletedPrizeDrawConfigs = async (
  hubspotClient: Client,
  lastSyncDate: string | undefined,
): Promise<(string | null)[]> => {
  const prizeDrawConfigObjectId = getPrizeDrawConfigObjectId();
  try {
    const response = await hubspotClient.crm.objects.basicApi.getPage(
      prizeDrawConfigObjectId,
      100,
      undefined,
      ["cuid", "hs_lastmodifieddate"],
      undefined,
      undefined,
      true, // archived items only
    );
    return response.results
      .filter(
        (result) =>
          !lastSyncDate ||
          (result.archivedAt &&
            new Date(result.archivedAt) >= new Date(lastSyncDate)),
      )
      .map((result) => result.properties.cuid);
  } catch (error) {
    console.error("Error fetching deleted prize draw configs:", error);
    return [];
  }
};

export const updateHubspotPrizeConfigsWithUuid = async (hubspotClient: Client, prizeDrawConfigs: HubspotPrizeDrawConfigWithPrizeDraw[]) => {
  if (prizeDrawConfigs.length === 0) return;
  const prizeDrawConfigObjectId = getPrizeDrawConfigObjectId();

  const updateObjects = prizeDrawConfigs.map(config => ({
    id: config.hs_record_id,
    properties: {
      cuid: config.cuid
    }
  }));

  await hubspotClient.crm.objects.batchApi.update(prizeDrawConfigObjectId, { inputs: updateObjects });
};