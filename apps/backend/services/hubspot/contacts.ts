import { Client } from "@hubspot/api-client";
import {
  Filter,
  FilterOperatorEnum,
  PublicObjectSearchRequest,
  SimplePublicObject,
} from "@hubspot/api-client/lib/codegen/crm/contacts";
import {
  BatchResponsePublicAssociationMulti,
  BatchResponsePublicAssociationMultiStatusEnum,
  BatchResponsePublicAssociationMultiWithErrors,
} from "@hubspot/api-client/lib/codegen/crm/associations";
import { User } from "../../entities/user";
import {
  getLastUserCreateSync,
  LastUserCreateSync,
} from "../../entities/lastUserCreateSync";
import { LastUserUpdateSync } from "../../entities/lastUserUpdateSync";
import { getPolicyDetailObjectId } from "./customObjectIds";

export interface ContactProperties {
  firstname: string;
  lastname: string;
  prize_draw_sync: string;
  insurance_policies?: PolicyDetails[];
}

export interface PolicyDetails {
  id: string;
  properties: {
    policy_number: string;
    policy_type: string;
    policy_status: string;
    policy_name: "Wanda ST 24" | "Wanda ST 25" | "Wanda ST MSM" | "Wanda Plus" | "Wanda AMT MSM";
  };
}

export const fetchNewContactsWithWandaPolicy = async (
  hubspotClient: Client,
  lastUserCreateSync: LastUserCreateSync | undefined,
): Promise<{ results: SimplePublicObject[] }> => {
  let allContacts: SimplePublicObject[] = [];
  let after: string | undefined = undefined;

  let filters: Filter[];
  if (!lastUserCreateSync) {
    filters = [
      {
        propertyName: "prize_draw_sync",
        operator: FilterOperatorEnum.Eq,
        value: "true",
      },
    ];
  } else {
    filters = [
      {
        propertyName: "prize_draw_sync",
        operator: FilterOperatorEnum.Eq,
        value: "true",
      },
      {
        propertyName: "createdate",
        operator: FilterOperatorEnum.Gte,
        value: lastUserCreateSync.date,
      },
    ];
  }

  do {
    const searchRequest: PublicObjectSearchRequest = {
      filterGroups: [
        {
          filters,
        },
      ],
      properties: [
        "firstname",
        "lastname",
        "prize_draw_sync",
        "iagl_baecmemberid",
        "email",
      ],
      limit: 100,
      after,
    };

    const response =
      await hubspotClient.crm.contacts.searchApi.doSearch(searchRequest);

    allContacts = [...allContacts, ...response.results];

    after = response.paging?.next?.after ?? undefined;
  } while (after);

  return { results: allContacts };
};

export const fetchUpdatedContactsWithWandaPolicy = async (
  hubspotClient: Client,
  lastUserUpdateSync: LastUserUpdateSync,
): Promise<{ results: SimplePublicObject[] }> => {
  let allContacts: SimplePublicObject[] = [];
  let after: string | undefined = undefined;

  const lastUserCreateSync = await getLastUserCreateSync();
  let date: string;
  if (!lastUserUpdateSync) {
    date = lastUserCreateSync.date;
  } else {
    date = lastUserUpdateSync.date;
  }

  do {
    const searchRequest: PublicObjectSearchRequest = {
      filterGroups: [
        {
          filters: [
            {
              propertyName: "prize_draw_sync",
              operator: FilterOperatorEnum.Eq,
              value: "true",
            },
            {
              propertyName: "lastmodifieddate",
              operator: FilterOperatorEnum.Gte,
              value: date,
            },
          ],
        },
      ],
      properties: [
        "firstname",
        "lastname",
        "prize_draw_sync",
        "iagl_baecmemberid",
        "email",
        "cuid",
      ],
      limit: 100,
      after,
    };

    const response =
      await hubspotClient.crm.contacts.searchApi.doSearch(searchRequest);

    allContacts = [...allContacts, ...response.results];

    after = response.paging?.next?.after ?? undefined;
  } while (after);

  return { results: allContacts };
};

export const fetchAssociatedPolicies = async (
  hubspotClient: Client,
  contactIds: string[],
): Promise<
  | BatchResponsePublicAssociationMultiWithErrors
  | BatchResponsePublicAssociationMulti
> => {
  const policyDetailObjectId = getPolicyDetailObjectId();
  const batchSize = 100;
  const results: (
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti
  )[] = [];

  // Process in batches of 100
  for (let i = 0; i < contactIds.length; i += batchSize) {
    const batch = contactIds.slice(i, i + batchSize);
    const batchResult = await hubspotClient.crm.associations.batchApi.read(
      "contacts",
      policyDetailObjectId,
      { inputs: batch.map((id) => ({ id })) },
    );
    results.push(batchResult);
  }

  return {
    status: BatchResponsePublicAssociationMultiStatusEnum.Complete,
    results: results.flatMap((result) => result.results),
    startedAt: results[0]?.startedAt,
    completedAt: results[results.length - 1]?.completedAt,
  };
};

export const createContactToAssociationsMap = (
  associationsResponse:
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti,
): Map<string, string[]> => {
  return new Map(
    associationsResponse.results.map((result) => [
      result._from.id,
      result.to.map((t) => t.id),
    ]),
  );
};

export const getAllPolicyIds = (
  associationsResponse:
    | BatchResponsePublicAssociationMultiWithErrors
    | BatchResponsePublicAssociationMulti,
): string[] => {
  return Array.from(
    new Set(
      associationsResponse.results.flatMap((result) =>
        result.to.map((t) => t.id),
      ),
    ),
  );
};

export const fetchPolicyDetails = async (
  hubspotClient: Client,
  policyIds: string[],
): Promise<Map<string, PolicyDetails>> => {
  const policyDetailObjectId = getPolicyDetailObjectId();
  if (policyIds.length === 0) return new Map();

  const batchSize = 100;
  const results: SimplePublicObject[] = [];

  // Process in batches of 100
  for (let i = 0; i < policyIds.length; i += batchSize) {
    const batch = policyIds.slice(i, i + batchSize);
    const batchResponse = await hubspotClient.crm.objects.batchApi.read(
      policyDetailObjectId,
      {
        properties: ["policy_number", "policy_type", "policy_status", "policy_name"],
        propertiesWithHistory: [],
        inputs: batch.map((id) => ({ id: id.toString() })),
      },
    );
    results.push(...batchResponse.results);
  }

  return new Map(
    results.map((policy) => [
      policy.id,
      policy as unknown as PolicyDetails,
    ]),
  );
};

export const combineContactsWithPolicies = (
  contacts: SimplePublicObject[],
  associationsMap: Map<string, string[]>,
  policiesMap: Map<string, PolicyDetails>,
): SimplePublicObject[] => {
  return contacts.map((contact) => {
    const contactAssociations = associationsMap.get(contact.id) || [];
    const policies = contactAssociations
      .map((assocId) => policiesMap.get(assocId))
      .filter((policy): policy is PolicyDetails => policy !== undefined);
    const isPolicyActive = getIsPolicyActive(policies);

    return {
      ...contact,
      properties: {
        ...contact.properties,
        isPolicyActive,
      },
    } as unknown as SimplePublicObject;
  });
};

export const getIsPolicyActive = (policies: PolicyDetails[]): boolean => {
  return policies.some(
    (policy) =>
      policy.properties.policy_status === "Active" &&
      policy.properties.policy_name === "Wanda Plus"
  );
};

export const updateContactsWithUuid = async (
  hubspotClient: Client,
  userItems: User[],
): Promise<void> => {
  const batchSize = 100;
  const results: SimplePublicObject[] = [];

  const updateObjects: UserCuid[] = userItems.map((user) => ({
    id: user.hubspotRecordId,
    properties: { cuid: user.userId },
  }));

  await updateContactsUuids(hubspotClient, updateObjects);
};

export type UserCuid = {
  id: string;
  properties: {
    cuid: string;
  };
};

export const updateContactsUuids = async (
  hubspotClient: Client,
  userCuids: UserCuid[],
): Promise<void> => {
  const batchSize = 100;
  const results: SimplePublicObject[] = [];

  for (let i = 0; i < userCuids.length; i += batchSize) {
    const batch = userCuids.slice(i, i + batchSize);
    const batchResponse = await hubspotClient.crm.contacts.batchApi.update({ inputs: batch });

    results.push(...batchResponse.results);
  }
};
