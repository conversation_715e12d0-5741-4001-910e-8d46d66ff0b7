import { SQ<PERSON><PERSON>, SQ<PERSON>Handler } from "aws-lambda";
import { RetrievePrizeFromDrawQueueMessage } from "../../constructs/sqs/retrievePrizeFromDraw-config";
import { queryNextPrizeSlot } from "../../entities/prizeSlots";
import { getEnvVariable } from "../../helpers/getEnvVariable";
import { updatePrizeDrawPlayTransaction } from "../../entities/prizeDrawPlays";
import { updatePrizeSlotToClaimedTransaction } from "../../entities/prizeSlots";
import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";

const prizeSlotTableName = getEnvVariable("PRIZE_SLOT_TABLE");

const dynamoDB = DynamoDBDocument.from(new DynamoDB());

export const handler: SQSHandler = async (event: SQSEvent) => {
  for (const record of event.Records) {
    const { userId, drawId, drawPlayId } = JSON.parse(
      record.body,
    ) as RetrievePrizeFromDrawQueueMessage;

    console.log(
      `Retrieving prize for user ${userId} from draw ${drawId} with draw play ${drawPlayId}`,
    );

    const prizeSlot = await queryNextPrizeSlot(drawId, prizeSlotTableName);

    if (!prizeSlot) {
      console.error(`No prize slot found for draw ${drawId}`);
      return;
    }

    try {
      await dynamoDB.transactWrite({
        TransactItems: [
          {
            Update: updatePrizeDrawPlayTransaction({
              userId,
              prizeDrawPlayId: drawPlayId,
              pointsWon: prizeSlot.rewardAmount,
            }),
          },
          {
            Update: updatePrizeSlotToClaimedTransaction({
              prizeSlot,
              prizeDrawPlayId: drawPlayId,
              tableName: prizeSlotTableName,
            }),
          },
        ],
      });
      console.log(
        `Successfully updated prize draw play and prize slot for user ${userId}`,
      );
    } catch (error) {
      console.error("Transaction failed:", error);
      throw error; // Let the SQS retry handle the failure
    }
  }
};
