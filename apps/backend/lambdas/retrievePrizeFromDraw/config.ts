import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { PolicyStatement, Effect } from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";
import { IQueue } from "aws-cdk-lib/aws-sqs";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";

interface RetrievePrizeFromDrawLambdaProps {
  platformTable: Table;
  prizeSlotTable: Table;
  sourceQueue: IQueue;
  stageName: string;
}

export class RetrievePrizeFromDrawLambda extends BaseLambda {
  constructor(scope: Construct, props: RetrievePrizeFromDrawLambdaProps) {
    const { platformTable, prizeSlotTable, sourceQueue, stageName } = props;
    const { stage } = getStage(scope);
    const id = `${stage}-retrievePrizeFromDrawLambda`;

    const policyStatements = [
      new PolicyStatement({
        resources: [platformTable.tableArn, `${platformTable.tableArn}/*`],
        actions: [
          "dynamodb:UpdateItem",
          "dynamodb:GetItem",
          "dynamodb:TransactWriteItems",
        ],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: [prizeSlotTable.tableArn, `${prizeSlotTable.tableArn}/*`],
        actions: [
          "dynamodb:UpdateItem",
          "dynamodb:Query",
          "dynamodb:TransactWriteItems",
        ],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: ["*"],
        actions: ["sqs:ReceiveMessage"],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        PRIZE_SLOT_TABLE: prizeSlotTable.tableName,
        SOURCE_QUEUE_URL: sourceQueue.queueUrl,
        STAGE_NAME: stageName,
      },
      policyStatements,
    });

    this.addEventSource(
      new SqsEventSource(sourceQueue, {
        batchSize: 1,
      }),
    );
  }
}
