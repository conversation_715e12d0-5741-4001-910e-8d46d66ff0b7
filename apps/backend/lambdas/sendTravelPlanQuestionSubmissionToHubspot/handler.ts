import { SQSE<PERSON>, <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "aws-lambda";
import { Client } from "@hubspot/api-client";
import { getUser } from "../../entities/user";
import { SendTravelPlanQuestionSubmissionToHubspotQueueMessage } from "../../constructs/sqs/sendTravelPlanQuestionSubmissionToHubspot-config";
import { updateItemDynamoDB } from "../helpers";
import { getEnvVariable } from '../../helpers/getEnvVariable';
import { getHubspotApiToken } from "../../services/hubspot/helpers";
import { sendTravelPlanQuestionSubmissionToHubspot } from "../../services/hubspot/travelPlanQuestionSubmissions";
import { getTravelPlanForm } from "../../entities/travelPlanForms";
import { getTravelPlanQuestion } from "../../entities/travelPlanQuestions";
import { getPrizeDraw } from "../../entities/prizeDraws";

const TABLE_NAME = getEnvVariable("PLATFORM_TABLE");

export const handler: SQSHandler = async (event: SQSEvent) => {
  for (const sqsRecord of event.Records) {
    const { record } = JSON.parse(sqsRecord.body) as SendTravelPlanQuestionSubmissionToHubspotQueueMessage;
    const newImage = record.dynamodb?.NewImage ?? {};
    try {
      const hubspotApiToken = await getHubspotApiToken();
      const hubspotClient = new Client({ accessToken: hubspotApiToken });

      const answer = newImage.answer.S ?? "";
      const prizeDrawId = newImage.prizeDrawId.S ?? "";
      const formId = newImage.formId.S ?? "";
      const questionId = newImage.questionId.S ?? "";
      const questionSubmissionId = newImage.questionSubmissionId.S ?? "";

      const hubspotPrizeDrawId = (await getPrizeDraw(prizeDrawId)).hubspotRecordId;

      const userId = newImage.userId.S;
      const hubspotUserId = (await getUser(`USER#${userId}`)).hubspotRecordId;

      const hubspotFormId = (await getTravelPlanForm(formId))?.hubspotRecordId;
      if (!hubspotFormId) {
        console.error(`Travel plan form ${formId}'s hubspot record id not found`);
        continue;
      }

      const travelPlanQuestion = await getTravelPlanQuestion(questionId);
      console.log(`Travel plan question: ${JSON.stringify(travelPlanQuestion)}`);
      const hubspotQuestionId = travelPlanQuestion?.hubspotRecordId;
      if (!hubspotQuestionId) {
        console.error(`Travel plan question ${questionId}'s hubspot record id not found`);
        continue;
      }


      const hubspotRecordId = await sendTravelPlanQuestionSubmissionToHubspot({
        hubspotClient,
        answer,
        hubspotPrizeDrawId,
        hubspotUserId,
        hubspotFormId,
        hubspotQuestionId,
        questionSubmissionId,
      });

      await updateItemDynamoDB({
        TableName: TABLE_NAME,
        Key: {
          pk: newImage.pk.S,
          sk: newImage.sk.S,
        },
        UpdateExpression: "SET #hubspotRecordId = :hubspotRecordId",
        ExpressionAttributeNames: {
          "#hubspotRecordId": "hubspotRecordId",
        },
        ExpressionAttributeValues: {
          ":hubspotRecordId": hubspotRecordId,
        },
      });
    } catch (error) {
      console.error("Error sending travel plan to HubSpot", error);
    }
  }
};
