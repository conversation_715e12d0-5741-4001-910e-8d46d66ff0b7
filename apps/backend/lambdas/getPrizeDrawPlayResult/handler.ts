import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { getPrizeDrawPlay } from "../../entities/prizeDrawPlays";

export const handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  try {
    const userId = event.queryStringParameters?.userId;
    const prizeDrawPlayId = event.queryStringParameters?.prizeDrawPlayId;

    if (!userId || !prizeDrawPlayId) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({ message: "Missing required query parameters" }),
      };
    }

    const prizeDrawPlay = await getPrizeDrawPlay(userId, prizeDrawPlayId);

    if (!prizeDrawPlay) {
      return {
        statusCode: 404,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({ message: "Prize draw play not found" }),
      };
    }

    const { pointsWon } = prizeDrawPlay;

    if (pointsWon === undefined) {
      return {
        statusCode: 202,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Credentials": true,
        },
        body: JSON.stringify({
          isPending: true,
          pointsWon,
        }),
      };
    }

    //await removeUserPlayingDrawLockTransaction(userId, prizeDrawPlay.prizeDrawId);

    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({
        pointsWon,
        isPending: false,
      }),
    };
  } catch (error) {
    console.error("Error getting prize draw play:", error);
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};
