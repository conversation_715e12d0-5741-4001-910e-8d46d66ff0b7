import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { PolicyStatement, Effect } from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";

interface GetPrizeDrawPlayResultLambdaProps {
  platformTable: Table;
  stageName: string;
}

export class GetPrizeDrawPlayResultLambda extends BaseLambda {
  constructor(scope: Construct, props: GetPrizeDrawPlayResultLambdaProps) {
    const { platformTable, stageName } = props;
    const { stage } = getStage(scope);
    const id = `${stage}-getPrizeDrawPlayResultLambda`;

    const policyStatements = [
      new PolicyStatement({
        resources: [platformTable.tableArn, `${platformTable.tableArn}/*`],
        actions: ["dynamodb:GetItem", "dynamodb:DeleteItem"],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        STAGE_NAME: stageName,
      },
      policyStatements,
    });
  }
}
