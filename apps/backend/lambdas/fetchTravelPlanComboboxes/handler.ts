import { Client } from '@hubspot/api-client';
import { v4 as uuidv4 } from "uuid";
import { 
  createObjectToAssociationsMap,
  fetchAssociatedObjects,
  fetchObjectDetails,
  getAllObjectIds,
  getHubspotApiToken 
} from '../../services/hubspot/helpers';
import { 
  combineTravelPlanComboboxWithAssoicatedTravelPlanFormCuid,
  fetchTravelPlanComboboxesFromHubspot,
  updateHubspotTravelPlanComboboxesWithUuid 
} from '../../services/hubspot/travelPlanComboboxes';
import { getLastTravelPlanComboboxesSync, updateLastTravelPlanComboboxesSync } from '../../entities/lastTravelPlanComboboxesSync';
import { updateTravelPlanComboboxesFromHubspot } from '../../entities/travelPlanComboboxes';
import { getAndDeleteArchivedTravelPlanComboboxes } from './helpers';
import { getTravelPlanComboboxObjectId, getTravelPlanQuestionObjectId } from '../../services/hubspot/customObjectIds';
import { getEnvVariable } from '../../helpers/getEnvVariable';

const STAGE_NAME = getEnvVariable("STAGE_NAME");

export const handler = async () => {
  try {
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });

    const lastTravelPlanComboboxesSync = await getLastTravelPlanComboboxesSync();
    const travelPlanComboboxes = (await fetchTravelPlanComboboxesFromHubspot(hubspotClient, lastTravelPlanComboboxesSync?.date)).results;

    const newTravelPlanComboboxes = travelPlanComboboxes.filter(combobox => {
      const createDate = new Date(combobox.hs_createdate);
      const lastSyncDate = lastTravelPlanComboboxesSync ? new Date(lastTravelPlanComboboxesSync.date) : new Date(0);
      return createDate >= lastSyncDate && combobox.travel_plan_multibox_cuid === null;
    });

    newTravelPlanComboboxes.forEach(combobox => {
      combobox.travel_plan_multibox_cuid = uuidv4();
    });
    
    const allTravelPlanComboboxes = travelPlanComboboxes.map(combobox => {
      const newCombobox = newTravelPlanComboboxes.find(newCombobox => newCombobox.hs_object_id === combobox.hs_object_id);
      return newCombobox || combobox;
    });

    const travelPlanComboboxIds = allTravelPlanComboboxes.map(combobox => combobox.hs_object_id);
    
    const travelPlanQuestionObjectId = getTravelPlanQuestionObjectId();
    const associatedTravelPlanQuestions = await fetchAssociatedObjects(
      hubspotClient,
      travelPlanComboboxIds,
      getTravelPlanComboboxObjectId(),
      travelPlanQuestionObjectId,
    );

    const associatedFormIds = getAllObjectIds(associatedTravelPlanQuestions);

    const associatedQuestionsMap = await fetchObjectDetails(
      hubspotClient,
      associatedFormIds,
      travelPlanQuestionObjectId,
      ["travel_plan_question_cuid"],
    );

    const objectAssociatedMap = createObjectToAssociationsMap(
      associatedTravelPlanQuestions,
    );

    const combinedTravelPlanComboboxObject = combineTravelPlanComboboxWithAssoicatedTravelPlanFormCuid(
      allTravelPlanComboboxes,
      objectAssociatedMap,
      associatedQuestionsMap,
    );

    if (STAGE_NAME === "prod" || STAGE_NAME === "staging") {
      await updateHubspotTravelPlanComboboxesWithUuid(hubspotClient, newTravelPlanComboboxes);
    } else {
      console.log("Travel Plan Combobox cuid not updated due to it being a local environment.");
    }
    

    await updateTravelPlanComboboxesFromHubspot({ comboboxes: combinedTravelPlanComboboxObject });

    await getAndDeleteArchivedTravelPlanComboboxes(hubspotClient, lastTravelPlanComboboxesSync?.date);
    
    await updateLastTravelPlanComboboxesSync();
  } catch (error) {
    console.error("Error syncing travel plan questions with Hubspot: ", error);
  };
};