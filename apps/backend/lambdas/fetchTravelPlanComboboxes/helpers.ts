import { Client } from '@hubspot/api-client';
import { fetchDeletedTravelPlanComboboxes } from '../../services/hubspot/travelPlanComboboxes';
import { batchDeleteTravelPlanComboboxes } from '../../entities/travelPlanComboboxes';

export const getAndDeleteArchivedTravelPlanComboboxes = async (hubspotClient: Client, lastSyncDate: string | undefined) => {
  const deletedQuestionIds = (await fetchDeletedTravelPlanComboboxes(hubspotClient, lastSyncDate)).filter(id => id !== null);

  if (deletedQuestionIds.length > 0) {
    console.log(`Deleting ${deletedQuestionIds.length} travel plan comboboxes from DynamoDB`);
    await batchDeleteTravelPlanComboboxes(deletedQuestionIds);
  }
}