import { Client } from "@hubspot/api-client";
import { putUser, User } from "../../entities/user";
import { HubspotUser } from "../types";
import { getEnvVariable } from "../../helpers/getEnvVariable";
import {
  getLastUserCreateSync,
  updateLastUserCreateSync,
} from "../../entities/lastUserCreateSync";
import { fetchNewContactsWithWandaPolicy, fetchAssociatedPolicies, createContactToAssociationsMap, getAllPolicyIds, fetchPolicyDetails, combineContactsWithPolicies, updateContactsWithUuid } from "../../services/hubspot/contacts";
import { getHubspotApiToken } from "../../services/hubspot/helpers";

const STAGE_NAME = getEnvVariable("STAGE_NAME");

export const handler = async (): Promise<void> => {
  try {
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });

    const lastUserCreateSync = await getLastUserCreateSync();
    const response = await fetchNewContactsWithWandaPolicy(
      hubspotClient,
      lastUserCreateSync,
    );
    const contactIds = response.results.map((result) => result.id);

    const associationsResponse = await fetchAssociatedPolicies(
      hubspotClient,
      contactIds,
    );
    const contactAssociationsMap =
      createContactToAssociationsMap(associationsResponse);

    const allPolicyIds = getAllPolicyIds(associationsResponse);
    const policiesMap = await fetchPolicyDetails(hubspotClient, allPolicyIds);

    const contactsWithPolicies = combineContactsWithPolicies(
      response.results,
      contactAssociationsMap,
      policiesMap,
    );

    const contacts: HubspotUser[] = contactsWithPolicies.map(
      (contact) => contact.properties as unknown as HubspotUser,
    );

    const newUserItems: User[] = [];
    await Promise.all(
      contacts.map(async (contact: HubspotUser) => {
        newUserItems.push(await putUser(contact));
      }),
    );

    if (STAGE_NAME === "prod" || STAGE_NAME === "staging") {
      console.log('FetchNewUsers: updating Hubspot CUIDs:', JSON.stringify(newUserItems))
      await updateContactsWithUuid(hubspotClient, newUserItems);
    } else {
      console.log("User cuid not updated due to it being a local environment.");
    }

    await updateLastUserCreateSync();
  } catch (error) {
    console.error(error);
  }
};
