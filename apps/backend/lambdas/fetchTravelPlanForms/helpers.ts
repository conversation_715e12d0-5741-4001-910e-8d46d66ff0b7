import { Client } from '@hubspot/api-client';
import { fetchDeletedTravelPlanForms } from '../../services/hubspot/travelPlanForms';
import { batchDeleteTravelPlanForms } from '../../entities/travelPlanForms';

export const getAndDeleteArchivedTravelPlanForms = async (hubspotClient: Client, lastSyncDate: string | undefined) => {
  const deletedFormIds = (await fetchDeletedTravelPlanForms(hubspotClient, lastSyncDate)).filter(id => id !== null);

  if (deletedFormIds.length > 0) {
    console.log(`Deleting ${deletedFormIds.length} travel plan forms from DynamoDB`);
    await batchDeleteTravelPlanForms(deletedFormIds);
  }
}