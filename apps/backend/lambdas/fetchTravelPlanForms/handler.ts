import { Client } from '@hubspot/api-client';
import { v4 as uuidv4 } from "uuid";
import { getHubspotApiToken } from '../../services/hubspot/helpers';
import { fetchTravelPlanFormsFromHubspot, updateHubspotTravelPlanFormsWithUuid } from '../../services/hubspot/travelPlanForms';
import { getLastTravelPlanFormsSync, updateLastTravelPlanFormsSync } from '../../entities/lastTravelPlanFormsSync';
import { updateTravelPlanFormsFromHubspot } from '../../entities/travelPlanForms';
import { getAndDeleteArchivedTravelPlanForms } from './helpers';
import { getEnvVariable } from '../../helpers/getEnvVariable';

const STAGE_NAME = getEnvVariable("STAGE_NAME");

export const handler = async () => {
  try {
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });

    const lastTravelPlanFormsSync = await getLastTravelPlanFormsSync();
    const travelPlanForms = (await fetchTravelPlanFormsFromHubspot(hubspotClient, lastTravelPlanFormsSync?.date)).results;

    const newTravelPlanForms = travelPlanForms.filter(form => {
      const createDate = new Date(form.hs_createdate);
      const lastSyncDate = lastTravelPlanFormsSync ? new Date(lastTravelPlanFormsSync.date) : new Date(0);
      return createDate >= lastSyncDate && form.travel_plan_form_cuid === null;
    });

    newTravelPlanForms.forEach(form => {
      form.travel_plan_form_cuid = uuidv4();
    });
    
    const allTravelPlanForms = travelPlanForms.map(form => {
      const newForm = newTravelPlanForms.find(newForm => newForm.hs_object_id === form.hs_object_id);
      return newForm || form;
    });

    if (STAGE_NAME === "prod" || STAGE_NAME === "staging") {
      await updateHubspotTravelPlanFormsWithUuid(hubspotClient, newTravelPlanForms);
    } else {
      console.log("Travel Plan Question cuid not updated due to it being a local environment.");
    }

    await updateTravelPlanFormsFromHubspot({ forms: allTravelPlanForms });

    await getAndDeleteArchivedTravelPlanForms(hubspotClient, lastTravelPlanFormsSync?.date);
    
    await updateLastTravelPlanFormsSync();
  } catch (error) {
    console.error("Error syncing travel plan forms with Hubspot: ", error);
  };
};