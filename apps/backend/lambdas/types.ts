

export interface AssociationResult {
  from: { id: string };
  to: Array<{ id: string }>;
}

export interface BatchAssociationResponse {
  results: AssociationResult[];
}

export interface HubspotUser {
  firstname: string;
  lastname: string;
  prize_draw_sync: boolean;
  iagl_baecmemberid: string | null;
  email: string;
  createdate: string;
  lastmodifieddate: string;
  isPolicyActive: boolean;
  hs_object_id: string;
  cuid: string;
}


