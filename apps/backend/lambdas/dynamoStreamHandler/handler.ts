import { DynamoD<PERSON><PERSON>amEvent, <PERSON>DBStream<PERSON>and<PERSON> } from "aws-lambda";
import { SendMessageCommand, SQSClient } from "@aws-sdk/client-sqs";
import { SendPrizeDrawPlayToHubspotQueueMessage } from "../../constructs/sqs/sendPrizeDrawPlayToHubspot-config";
import { SendTravelPlanQuestionSubmissionToHubspotQueueMessage } from "../../constructs/sqs/sendTravelPlanQuestionSubmissionToHubspot-config";

const sqsClient = new SQSClient();

export const handler: <PERSON>D<PERSON><PERSON>amHand<PERSON> = async (
  event: DynamoDBStreamEvent,
) => {
  for (const record of event.Records) {
    const newImage = record.dynamodb?.NewImage;
    if (record.eventName === "MODIFY") {
      const oldImage = record.dynamodb?.OldImage;
      if (!oldImage || !newImage) continue;
      if (
        newImage.pk?.S?.startsWith("USER#") &&
        newImage.sk?.S?.startsWith("DRAWS#PRIZE_DRAW_PLAY_ID#")
      ) {
        const message: SendPrizeDrawPlayToHubspotQueueMessage = {
          record,
        };
        await sqsClient.send(
          new SendMessageCommand({
            QueueUrl: process.env.PRIZE_DRAW_PLAY_QUEUE_URL,
            MessageBody: JSON.stringify(message),
          }),
        );
      }
    }
    else if (record.eventName === "INSERT") {
      if (!newImage) return;
      if (
        newImage.pk?.S?.startsWith("USER#") &&
        newImage.sk?.S?.startsWith("TRAVEL_PLAN_QUESTION_SUBMISSION#")
      ) {
        const message: SendTravelPlanQuestionSubmissionToHubspotQueueMessage = {
          record,
        };
        await sqsClient.send(
          new SendMessageCommand({
            QueueUrl: process.env.TRAVEL_PLAN_QUESTION_SUBMISSION_QUEUE_URL,
            MessageBody: JSON.stringify(message),
          }),
        );
      }
    }
  }
};
