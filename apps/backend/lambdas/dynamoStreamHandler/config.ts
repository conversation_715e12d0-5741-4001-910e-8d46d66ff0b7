import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";
import { DynamoEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import { StartingPosition } from "aws-cdk-lib/aws-lambda";
import { Effect, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { Queue } from "aws-cdk-lib/aws-sqs";

interface DynamoStreamHandlerProps {
  platformTable: Table;
  prizeDrawPlayQueue: Queue;
  travelPlanQuestionSubmissionQueue: Queue;
  stageName: string;
}

export class DynamoStreamHandlerLambda extends BaseLambda {
  constructor(scope: Construct, props: DynamoStreamHandlerProps) {
    const {
      platformTable,
      prizeDrawPlayQueue,
      travelPlanQuestionSubmissionQueue,
      stageName,
    } = props;
    const { stage } = getStage(scope);
    const id = `${stage}DynamoStreamHandlerLambda`;

    const policyStatements = [
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          "dynamodb:DescribeStream",
          "dynamodb:GetRecords",
          "dynamodb:GetShardIterator",
          "dynamodb:ListStreams",
        ],
        resources: [platformTable.tableStreamArn!],
      }),
      new PolicyStatement({
        resources: [
          prizeDrawPlayQueue.queueArn,
          travelPlanQuestionSubmissionQueue.queueArn
        ],
        actions: ["sqs:SendMessage"],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        PRIZE_DRAW_PLAY_QUEUE_URL: prizeDrawPlayQueue.queueUrl,
        TRAVEL_PLAN_QUESTION_SUBMISSION_QUEUE_URL: travelPlanQuestionSubmissionQueue.queueUrl,
        STAGE_NAME: stageName,
      },
      policyStatements,
      timeout: 300,
    });

    this.addEventSource(
      new DynamoEventSource(platformTable, {
        startingPosition: StartingPosition.LATEST,
        batchSize: 100,
        retryAttempts: 1,
      }),
    );
  }
}
