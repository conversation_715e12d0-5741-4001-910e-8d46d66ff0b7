import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createLambdaResponse } from "../helpers";
import { getTravelPlanForm } from "../../entities/travelPlanForms";
import { queryTravelPlanQuestionsForForm, TravelPlanQuestion } from "../../entities/travelPlanQuestions";
import { queryTravelPlanComboboxesForQuestion } from "../../entities/travelPlanComboboxes";

type QuestionWithCombobox = TravelPlanQuestion & {
  comboboxOptions?: string[];
};

export const handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  if (!event.queryStringParameters) {
    return createLambdaResponse(
      400,
      "Invalid request, you are missing the formId query parameter.",
    );
  }

  const formId = event.queryStringParameters.formId;

  if (!formId) {
    return createLambdaResponse(
      400,
      "Invalid request, you are missing the formId query parameter."
    );
  }

  const travelPlanForm = await getTravelPlanForm(formId);

  if (!travelPlanForm) {
    return createLambdaResponse(
      404,
      "Travel plan form not found",
    );
  }

  const travelPlanQuestions = await queryTravelPlanQuestionsForForm(formId);

  const questionsWithComboboxes = await Promise.all(
    travelPlanQuestions.map(async (question): Promise<QuestionWithCombobox> => {
      if (question.questionFormat !== "Combobox") {
        return question;
      }

      const travelPlanComboboxes = await queryTravelPlanComboboxesForQuestion(question.questionId);
      const comboboxOptions = travelPlanComboboxes.map(combobox => combobox.travelPlanComboboxOptionText);
      
      return {
        ...question,
        comboboxOptions,
      };
    })
  );
  
  return createLambdaResponse(
    200,
    JSON.stringify({
      formTitle: travelPlanForm.formTitle,
      questions: questionsWithComboboxes.reverse().map(question => ({
        question: question.question,
        questionId: question.questionId,
        questionFormat: question.questionFormat,
        questionPlaceholderText: question.questionPlaceholderText,
        comboboxOptions: question.comboboxOptions,
      })),
    }),
  );
};
