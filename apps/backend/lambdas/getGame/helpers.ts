import * as crypto from "crypto";
import { queryTravelPlanForms, TravelPlanForm } from "../../entities/travelPlanForms";
import { getMostRecentTravelPlanSubmission, queryAllTravelPlanSubmissions } from "../../entities/travelPlanSubmissions";

const getRandomFloat = (max: number) => {
  const buffer = crypto.randomBytes(4);
  const randomInt = buffer.readUInt32BE(0);
  const randomFloat =  randomInt / 0xFFFFFFFF;

  return randomFloat * max;
}

export const getFormFromWeightedProbabilities = (
  forms: TravelPlanForm[]
): string | undefined => {
  const totalWeightedProbability = forms.reduce((sum, form) => sum + form.weightedProbability, 0);
  let randomNumber = getRandomFloat(totalWeightedProbability);
  forms.sort((a, b) => a.weightedProbability - b.weightedProbability);

  for (const form of forms) {
    randomNumber -= form.weightedProbability;
    if (randomNumber <= 0) {
      return form.travelPlanFormId;
    }
  }

  return undefined;
};

export const determineEligibleForms = async (userId: string) => {
  const mostRecentTravelPlanSubmission = await getMostRecentTravelPlanSubmission(userId);
  const allTravelPlanSubmissions = await queryAllTravelPlanSubmissions(userId);
  const travelPlanForms = await queryTravelPlanForms();


  // Initialize dictionary with all forms starting at 0
  const formSubmissionCounts: Record<string, number> = travelPlanForms.reduce((acc, form) => {
    acc[form.travelPlanFormId] = 0;
    return acc;
  }, {} as Record<string, number>);
  
  
  // Count submissions for each form
  allTravelPlanSubmissions.forEach(submission => {
    if (submission.formId) {
      formSubmissionCounts[submission.formId]++;
    }
  });

  // Remove the most recent submission's form from the counts dictionary as we dont want to show it again
  if (mostRecentTravelPlanSubmission?.formId) {
    delete formSubmissionCounts[mostRecentTravelPlanSubmission.formId];
  }

  const formsWithZeroSubmissions = Object.entries(formSubmissionCounts)
    .filter(([, count]) => count === 0)
    .map(([formId]) => formId);

  if (formsWithZeroSubmissions.length > 0) {
    // Get forms that have never been submitted for user
    return formsWithZeroSubmissions
      .map(formId => travelPlanForms.find(form => form.travelPlanFormId === formId))
      .filter((form): form is TravelPlanForm => form !== undefined);
  }

  //If all forms been submitted before, just return all forms except most recent form
  return travelPlanForms.filter(form => form.travelPlanFormId in formSubmissionCounts);

}