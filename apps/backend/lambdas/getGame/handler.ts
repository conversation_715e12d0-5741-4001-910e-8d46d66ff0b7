import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { queryPrizeDrawPlays } from "../../entities/prizeDrawPlays";
import { getUser } from "../../entities/user";
import { createLambdaResponse } from "../helpers";
import { getActivePrizeDrawId } from "../../entities/prizeDraws";
import { determineEligibleForms, getFormFromWeightedProbabilities } from "./helpers";
import { queryTravelPlanSubmissionsForPrizeDraw } from "../../entities/travelPlanSubmissions";

export enum GameState {
  PENDING_FIRST_PLAY = "PENDING_FIRST_PLAY",
  PENDING_CONFIRM_TRAVEL_PLANS = "PENDING_CONFIRM_TRAVEL_PLANS",
  PENDING_SECOND_PLAY = "PENDING_SECOND_PLAY",
  OUT_OF_DRAWS = "OUT_OF_DRAWS",
}

enum AccountState {
  VALID = "VALID",
  NO_AVIOS_ACCOUNT_LINKED = "NO_AVIOS_ACCOUNT_LINKED",
  WANDA_POLICY_INACTIVE = "WANDA_POLICY_INACTIVE",
  PRIZE_DRAW_NOT_ACTIVE = "PRIZE_DRAW_NOT_ACTIVE",
}

export const handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  if (!event.queryStringParameters) {
    return createLambdaResponse(
      400,
      "Invalid request, you are missing the userId query parameter.",
    );
  }

  const userId = event.queryStringParameters.userId;

  if (!userId) {
    return {
      statusCode: 400,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: "Invalid request, you are missing the userId query parameter.",
    };
  }

  const partitionKey = `USER#${userId}`;

  const user = await getUser(partitionKey);

  if (!user) {
    return {
      statusCode: 404,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": true,
      },
      body: "User not found",
    };
  }

  const { prizeDrawId, isActive } = await getActivePrizeDrawId();

  if (!prizeDrawId) {
    return createLambdaResponse(
      404,
      "No active prize draw found",
    );
  }

  const numberOfDraws = await queryPrizeDrawPlays(
    partitionKey,
    prizeDrawId,
  ).then((result) => result.length ?? 0);
  const numberOfFormsSubmitted = await queryTravelPlanSubmissionsForPrizeDraw({ userId, prizeDrawId: prizeDrawId }).then((result) => result.length ?? 0);

  let gameState: string;
  if (numberOfDraws === 0) {
    gameState = GameState.PENDING_FIRST_PLAY;
  } else if (numberOfDraws === 1 && numberOfFormsSubmitted === 0) {
    gameState = GameState.PENDING_CONFIRM_TRAVEL_PLANS;
  } else if (numberOfDraws === 1 && numberOfFormsSubmitted === 1) {
    gameState = GameState.PENDING_SECOND_PLAY;
  } else {
    gameState = GameState.OUT_OF_DRAWS;
  }

  let accountState: AccountState = AccountState.VALID;
  if (!isActive) {
    accountState = AccountState.PRIZE_DRAW_NOT_ACTIVE;
  } else if (!user.isPolicyActive) {
    accountState = AccountState.WANDA_POLICY_INACTIVE;
  } else if (!user.baecMemberId) {
    accountState = AccountState.NO_AVIOS_ACCOUNT_LINKED;
  }

  let formId = undefined;
  if (GameState.PENDING_CONFIRM_TRAVEL_PLANS === gameState) {
    const eligibleForms = await determineEligibleForms(userId);
    formId = await getFormFromWeightedProbabilities(eligibleForms);
  }

  return createLambdaResponse(
    200,
    JSON.stringify({
      gameState,
      accountState,
      formId,
    }),
  );
};
