import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { PolicyStatement, Effect } from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";

interface AddGuaranteedPrizeToSlotsLambdaProps {
  prizeSlotTable: Table;
  stageName: string;
}

export class AddGuaranteedPrizeToSlotsLambda extends BaseLambda {
  constructor(scope: Construct, props: AddGuaranteedPrizeToSlotsLambdaProps) {
    const { prizeSlotTable, stageName } = props;
    const { stage } = getStage(scope);
    const id = `${stage}AddGuaranteedPrizeToSlotsLambda`;

    const policyStatements = [
      new PolicyStatement({
        resources: [prizeSlotTable.tableArn, `${prizeSlotTable.tableArn}/*`],
        actions: ["dynamodb:PutItem"],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PRIZE_SLOT_TABLE: prizeSlotTable.tableName,
        STAGE_NAME: stageName,
      },
      policyStatements,
    });
  }
}
