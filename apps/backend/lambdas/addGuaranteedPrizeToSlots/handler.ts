import { addGuaranteedPrizeToSlots } from "../../entities/prizeSlots";
import { getEnvVariable } from "../../helpers/getEnvVariable";

interface AddGuaranteedPrizeToSlotsEvent {
  prizeDrawId: string;
  slotId: string;
  year: number;
  month: string;
  rewardAmount: number;
  source: string;
}

export const handler = async (event: AddGuaranteedPrizeToSlotsEvent) => {
  const { prizeDrawId, slotId, year, month, rewardAmount, source } = event;
  const prizeSlotTableName = getEnvVariable("PRIZE_SLOT_TABLE");
  if (source !== "addGuaranteedPrizeToSlots") {
    throw new Error("Invalid event source");
  }

  try {
    await addGuaranteedPrizeToSlots({
      rewardAmount,
      prizeDrawId,
      slotId,
      year,
      month,
      tableName: prizeSlotTableName,
    });
  } catch (error) {
    console.error("Error adding guaranteed prize to slots:", error);
    throw error;
  }
};
