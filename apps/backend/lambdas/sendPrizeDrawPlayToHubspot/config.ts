import { Construct } from "constructs";
import { Queue } from "aws-cdk-lib/aws-sqs";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { PolicyStatement, Effect } from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";
import { Table } from 'aws-cdk-lib/aws-dynamodb';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';

interface SendPrizeDrawPlayToHubspotLambdaProps {
  sourceQueue: Queue;
  platformTable: Table;
  hubspotSecret: Secret;
  stageName: string;
}

export class SendPrizeDrawPlayToHubspotLambda extends BaseLambda {
  constructor(scope: Construct, props: SendPrizeDrawPlayToHubspotLambdaProps) {
    const { sourceQueue, platformTable, hubspotSecret, stageName } = props;
    const { stage } = getStage(scope);
    const id = `${stage}SendPrizeDrawPlayToHubspotLambda`;

    const policyStatements = [
      new PolicyStatement({
        resources: [sourceQueue.queueArn],
        actions: ["sqs:ReceiveMessage"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        actions: ["secretsmanager:GetSecretValue"],
        resources: [hubspotSecret.secretArn],
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        HUBSPOT_API_TOKEN_SECRET_NAME: hubspotSecret.secretName,
        STAGE_NAME: stageName,
      },
      policyStatements,
    });

    this.addEventSource(
      new SqsEventSource(sourceQueue, {
        batchSize: 1,
      }),
    );
  }
}
