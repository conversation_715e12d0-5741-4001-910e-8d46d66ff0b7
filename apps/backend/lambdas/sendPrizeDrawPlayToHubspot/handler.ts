import { SQSE<PERSON>, SQSHandler } from "aws-lambda";
import { Client } from "@hubspot/api-client";
import { getUser } from "../../entities/user";
import { getPrizeDraw } from "../../entities/prizeDraws";
import { getHubspotApiToken } from "../../services/hubspot/helpers";
import { sendPrizeDrawPlayToHubspot } from "../../services/hubspot/prizeDrawPlays";
import { SendPrizeDrawPlayToHubspotQueueMessage } from "../../constructs/sqs/sendPrizeDrawPlayToHubspot-config";

export const handler: SQSHandler = async (event: SQSEvent) => {
  for (const sqsRecord of event.Records) {
    const { record } = JSON.parse(
      sqsRecord.body,
    ) as SendPrizeDrawPlayToHubspotQueueMessage;
    console.log("record: ", JSON.stringify(record));
    const newImage = record.dynamodb?.NewImage ?? {};
    const oldImage = record.dynamodb?.OldImage ?? {};

    const oldPointsWon = oldImage.pointsWon?.N;
    const newPointsWon = newImage.pointsWon?.N;
    console.log("Old points won", oldPointsWon);
    console.log("New points won", newPointsWon);

    if (oldPointsWon || !newPointsWon) continue;

    try {
      const hubspotApiToken = await getHubspotApiToken();
      const hubspotClient = new Client({ accessToken: hubspotApiToken });

      const userId = newImage.userId.S;
      const user = await getUser(`USER#${userId}`);
      const hubspotUserId = user.hubspotRecordId;

      const prizeDrawId = newImage.prizeDrawId.S ?? "";
      const hubspotPrizeDrawId = (await getPrizeDraw(prizeDrawId))
        .hubspotRecordId;
      const prizeDrawPlayId = newImage.prizeDrawPlayId.S ?? "";

      const timeStamp = newImage.timeStamp.S ?? "";

      const response = await sendPrizeDrawPlayToHubspot(
        hubspotClient,
        newPointsWon,
        prizeDrawPlayId,
        hubspotPrizeDrawId,
        hubspotUserId,
        timeStamp,
      );
      console.log("Response from HubSpot", response);
    } catch (error) {
      console.error("Error sending prize draw play to HubSpot", error);
    }
  }
};
