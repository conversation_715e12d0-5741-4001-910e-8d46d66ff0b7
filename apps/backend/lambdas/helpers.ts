import { DynamoDB, QueryCommandInput } from "@aws-sdk/client-dynamodb";
import {
  GetSecretValueCommand,
  SecretsManager,
} from "@aws-sdk/client-secrets-manager";
import {
  BatchWriteCommandInput,
  DeleteCommandInput,
  DynamoDBDocument,
  GetCommandInput,
  PutCommandInput,
  UpdateCommandInput,
} from "@aws-sdk/lib-dynamodb";
import { APIGatewayProxyResult } from "aws-lambda";

const db = DynamoDBDocument.from(new DynamoDB());
const secretsManager = new SecretsManager();

export const getItemDynamoDB = async <T>(
  params: GetCommandInput,
): Promise<{ Item: T }> => {
  try {
    const response = await db.get(params);
    return { Item: response.Item as T };
  } catch (error) {
    console.error("DynamoDB Query Error:", error);
    throw new Error("Error fetching items from DynamoDB.");
  }
};

export const queryDynamoDB = async <T>(
  params: QueryCommandInput,
): Promise<{ Items?: T[] }> => {
  try {
    const response = await db.query(params);
    return { Items: response.Items as T[] };
  } catch (error) {
    console.error("DynamoDB Query Error:", error);
    throw new Error("Error fetching items from DynamoDB.");
  }
};

export const putItemDynamoDB = async (
  params: PutCommandInput,
): Promise<void> => {
  try {
    await db.put(params);
  } catch (error) {
    console.error("DynamoDB PutItem Error:", error);
    throw new Error("Error putting item into DynamoDB.");
  }
};

export const deleteItemDynamoDB = async (
  params: DeleteCommandInput,
): Promise<void> => {
  try {
    await db.delete(params);
  } catch (error) {
    console.error("DynamoDB DeleteItem Error:", error);
    throw new Error("Error deleting item from DynamoDB.");
  }
};

export const updateItemDynamoDB = async (
  params: UpdateCommandInput,
): Promise<void> => {
  try {
    await db.update(params);
  } catch (error) {
    console.error("DynamoDB UpdateItem Error:", error);
    throw new Error("Error updating item in DynamoDB.");
  }
};

export const batchWriteItemsDynamoDB = async (
  params: BatchWriteCommandInput,
): Promise<void> => {
  try {
    await db.batchWrite(params);
  } catch (error) {
    console.error("DynamoDB BatchWriteItem Error:", error);
    throw new Error("Error batch writing items into DynamoDB.");
  }
};

export const createLambdaResponse = (
  statusCode: number,
  body: string,
): APIGatewayProxyResult => {
  return {
    statusCode,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
    },
    body,
  };
};

export const getSecret = async (secretName: string): Promise<string> => {
  const command = new GetSecretValueCommand({ SecretId: secretName });
  const response = await secretsManager.send(command);
  if (!response.SecretString) {
    throw new Error(`Secret ${secretName} not found`);
  }
  return response.SecretString;
};
