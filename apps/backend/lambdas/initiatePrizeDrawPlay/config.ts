import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { PolicyStatement, Effect } from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";

interface InitiatePrizeDrawPlayLambdaProps {
  platformTable: Table;
  retrievePrizeFromDrawQueueUrl: string;
}

export class InitiatePrizeDrawPlayLambda extends BaseLambda {
  constructor(scope: Construct, props: InitiatePrizeDrawPlayLambdaProps) {
    const { platformTable, retrievePrizeFromDrawQueueUrl } = props;
    const { stage } = getStage(scope);
    const id = `${stage}-initiatePrizeDrawPlayLambda`;

    const policyStatements = [
      new PolicyStatement({
        resources: [platformTable.tableArn, `${platformTable.tableArn}/*`],
        actions: ["dynamodb:PutItem", "dynamodb:GetItem", "dynamodb:Query"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: ["*"],
        actions: [
          "sqs:SendMessage",
        ],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        RETRIEVE_PRIZE_FROM_DRAW_QUEUE_URL: retrievePrizeFromDrawQueueUrl,
      },
      policyStatements,
    });
  }
}
