import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { SendMessageCommand, SQSClient } from "@aws-sdk/client-sqs";
import { getActivePrizeDrawId } from "../../entities/prizeDraws";
import { v4 as uuidv4 } from "uuid";
import { RetrievePrizeFromDrawQueueMessage } from "../../constructs/sqs/retrievePrizeFromDraw-config";
import { putInitialPrizeDrawPlay, queryPrizeDrawPlays } from "../../entities/prizeDrawPlays";
import { putUserPlayingDrawLock } from "../../entities/user";
import { createLambdaResponse } from "../helpers";
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

export const sqsClient = new SQSClient();

const ajv = new Ajv();
addFormats(ajv);

const schema = {
  type: 'object',
  properties: {
    userId: { type: 'string', format: 'uuid' },
  },
  required: [],
  additionalProperties: true,
};

const validatePayload = ajv.compile<any>(schema);

export const handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return createLambdaResponse(400, "Missing request body");
    }
    const body = JSON.parse(event.body);

    if (!validatePayload(body)) {
      return createLambdaResponse(400, JSON.stringify({ message: "Invalid userId in request body" }));
    }
    
    const { userId } = body;

    if (!userId) {
      return createLambdaResponse(400, JSON.stringify({ message: "Missing userId in request body" }));
    }

    const { prizeDrawId } = await getActivePrizeDrawId();

    if (!prizeDrawId) {
      return createLambdaResponse(500, "No active prize draw found");
    }

    const existingPlays = await queryPrizeDrawPlays(`USER#${userId}`, prizeDrawId);
  
    if (existingPlays.length >= 2) {
      return createLambdaResponse(400, "Maximum number of prize draw plays reached for this draw");
    }

    try {
      await putUserPlayingDrawLock(userId, prizeDrawId);
    } catch (error) {
      console.error("Error:", error);
      return createLambdaResponse(500, "Error putting user playing draw lock");
    }

    const drawPlayId = uuidv4();

    await putInitialPrizeDrawPlay(userId, prizeDrawId, drawPlayId);

    const message: RetrievePrizeFromDrawQueueMessage = {
      drawId: prizeDrawId,
      userId,
      drawPlayId,
    };

    // Send message to SQS queue
    await sqsClient.send(
      new SendMessageCommand({
        QueueUrl: process.env.RETRIEVE_PRIZE_FROM_DRAW_QUEUE_URL,
        MessageBody: JSON.stringify(message),
        MessageGroupId: userId,
      }),
    );

    return createLambdaResponse(200, JSON.stringify({
        message: "Prize draw initiated successfully",
        drawId: prizeDrawId,
        drawPlayId,
      }),
    );
  } catch (error) {
    console.error("Error:", error);
    return createLambdaResponse(500, "Internal server error");
  }
};
