import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";

import { createTravelPlanSubmission } from "../../entities/travelPlanSubmissions";
import { createLambdaResponse } from "../helpers";
import { createTravelPlanQuestionSubmission } from "../../entities/travelPlanQuestionSubmissions";
import { getActivePrizeDrawId } from "../../entities/prizeDraws";

interface TravelPlansRequestBody {
  userId: string;
  formId: string;
  questionsWithAnswers: {
    questionId: string;
    answer: string;
  }[];
}

export const handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  if (!event.body) {
    return createLambdaResponse(
      400,
      "Invalid request, you are missing the parameters in the body.",
    );
  }

  const { userId, formId, questionsWithAnswers } = JSON.parse(
    event.body,
  ) as TravelPlansRequestBody;

  if (!userId || !formId || !questionsWithAnswers) {
    return createLambdaResponse(
      400,
      "Invalid request, you are missing the parameter body that contains the userId, formId, and questionsWithAnswers.",
    );
  }

  const { prizeDrawId } = await getActivePrizeDrawId();

  if (!prizeDrawId) {
    return createLambdaResponse(
      500,
      "No active prize draw found",
    );
  }

  // Create travel plan submission, and then store the specific answers for the submission in separate items.
  const travelPlanSubmission = await createTravelPlanSubmission({ userId, prizeDrawId, formId });
  const { formSubmittedId } = travelPlanSubmission;

  for (const questionWithAnswer of questionsWithAnswers) {
    const { questionId, answer } = questionWithAnswer;
    await createTravelPlanQuestionSubmission({ userId, formId, formSubmittedId, prizeDrawId, questionId, answer });
  }


  return createLambdaResponse(201, `Travel plans submitted for user ${userId}`);
};
