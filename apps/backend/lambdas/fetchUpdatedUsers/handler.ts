import { v4 as uuidv4 } from "uuid";

import { Client } from "@hubspot/api-client";
import { updateUser } from "../../entities/user";
import { HubspotUser } from "../types";
import { getEnvVariable } from "../../helpers/getEnvVariable";
import {
  getLastUserUpdateSync,
  updateLastUserUpdateSync,
} from "../../entities/lastUserUpdateSync";
import { getHubspotApiToken } from "../../services/hubspot/helpers";
import { combineContactsWithPolicies, createContactToAssociationsMap, fetchAssociatedPolicies, fetchPolicyDetails, fetchUpdatedContactsWithWandaPolicy, getAllPolicyIds, updateContactsUuids, UserCuid } from "../../services/hubspot/contacts";

const STAGE_NAME = getEnvVariable("STAGE_NAME");

export const handler = async (): Promise<void> => {
  try {
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });

    const lastUserUpdateSync = await getLastUserUpdateSync();
    console.log(`FetchUpdatedUsers: retrieving after ${lastUserUpdateSync?.date ?? 'unknown'}`);
    const response = await fetchUpdatedContactsWithWandaPolicy(
      hubspotClient,
      lastUserUpdateSync,
    );
    const contactIds = response.results.map((contact) => contact.id);
    console.log(`FetchUpdatedUsers: contactIds: `, JSON.stringify(contactIds));

    const associationsResponse = await fetchAssociatedPolicies(
      hubspotClient,
      contactIds,
    );
    const contactAssociationsMap =
      createContactToAssociationsMap(associationsResponse);

    const allPolicyIds = getAllPolicyIds(associationsResponse);
    const policiesMap = await fetchPolicyDetails(hubspotClient, allPolicyIds);

    const contactsWithPolicies = combineContactsWithPolicies(
      response.results,
      contactAssociationsMap,
      policiesMap,
    );

    const contacts: HubspotUser[] = contactsWithPolicies.map(
      (contact) => contact.properties as unknown as HubspotUser,
    );

    const newUserCuids: UserCuid[] = [];
    await Promise.all(
      contacts.map(async (contact: HubspotUser) => {
        if (!contact.cuid) { // This could happen if update-user happens before fetch-new user
          contact.cuid = uuidv4();
          newUserCuids.push({
            id: contact.hs_object_id,
            properties: {
              cuid: contact.cuid
            }
          });
        }
        const partitionKey = `USER#${contact.cuid}`;
        await updateUser(partitionKey, contact);
      }),
    );
    if (STAGE_NAME === "prod" || STAGE_NAME === "staging") {
      console.log('FetchUpdatedUsers: updating Hubspot CUIDs:', JSON.stringify(newUserCuids))
      await updateContactsUuids(hubspotClient, newUserCuids);
    } else {
      console.log("User cuid not updated due to it being a local environment.");
    }
    await updateLastUserUpdateSync();
  } catch (error) {
    console.error(error);
  }
};
