import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import {
  PolicyStatement,
  Effect,
  ServicePrincipal,
  Role,
} from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";
import { Secret } from "aws-cdk-lib/aws-secretsmanager";

interface StartPrizeDrawLambdaProps {
  platformTable: Table;
  prizeSlotTable: Table;
  addGuaranteedPrizeToSlotsLambda: BaseLambda;
  hubspotApiTokenSecret: Secret;
  stageName: string;
}

export class StartPrizeDrawLambda extends BaseLambda {
  constructor(scope: Construct, props: StartPrizeDrawLambdaProps) {
    const { platformTable, prizeSlotTable, addGuaranteedPrizeToSlotsLambda, hubspotApiTokenSecret, stageName } = props;
    const { stage } = getStage(scope);
    const id = `${stage}StartPrizeDrawLambda`;

    // scheduler role for addGuaranteedPrizeToSlotsLambda
    const addGuaranteedPrizeToSlotsLambdaRole = new Role(
      scope,
      `${stage}AddGuaranteedPrizeToSlotsLambdaRole`,
      {
        roleName: `${stage}-add-guaranteed-prize-to-slots-lambda-role`,
        assumedBy: new ServicePrincipal("scheduler.amazonaws.com"),
      },
    );

    addGuaranteedPrizeToSlotsLambdaRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ["lambda:InvokeFunction"],
        resources: [addGuaranteedPrizeToSlotsLambda.functionArn],
      }),
    );

    const addGuaranteedPrizeToSlotsSchedulerRoleArn =
      addGuaranteedPrizeToSlotsLambdaRole.roleArn;

    const policyStatements = [
      new PolicyStatement({
        resources: [prizeSlotTable.tableArn, `${prizeSlotTable.tableArn}/*`],
        actions: ["dynamodb:BatchWriteItem", "dynamodb:Query"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: [platformTable.tableArn, `${platformTable.tableArn}/*`],
        actions: ["dynamodb:UpdateItem", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:GetItem", "dynamodb:DeleteItem", "dynamodb:BatchWriteItem"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: ["*"],
        actions: ["scheduler:CreateSchedule"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: [addGuaranteedPrizeToSlotsSchedulerRoleArn],
        actions: ["iam:PassRole"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        actions: ["secretsmanager:GetSecretValue"],
        resources: [hubspotApiTokenSecret.secretArn],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PRIZE_SLOT_TABLE: prizeSlotTable.tableName,
        PLATFORM_TABLE: platformTable.tableName,
        ADD_GUARANTEED_PRIZE_TO_SLOTS_LAMBDA_ARN:
          addGuaranteedPrizeToSlotsLambda.functionArn,
        ADD_GUARANTEED_PRIZE_TO_SLOTS_SCHEDULER_ROLE_ARN:
          addGuaranteedPrizeToSlotsSchedulerRoleArn,
        HUBSPOT_API_TOKEN_SECRET_NAME: hubspotApiTokenSecret.secretName,
        STAGE_NAME: stageName,
      },
      policyStatements,
      timeout: 300,
    });
  }
}
