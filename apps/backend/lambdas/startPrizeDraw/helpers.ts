import { PrizeSlot } from "../../entities/prizeSlots";
import * as crypto from "crypto";
import { v4 as uuidv4 } from "uuid";
import {
  CreateScheduleCommand,
  SchedulerClient,
} from "@aws-sdk/client-scheduler";
import { getEnvVariable } from "../../helpers/getEnvVariable";
import { Client } from "@hubspot/api-client";
import { getLastPrizeDrawConfigSync, updateLastPrizeDrawConfigSync } from "../../entities/lastPrizeDrawConfigSync";
import { updatePrizeDrawConfigsFromHubspot } from "../../entities/prizeDrawConfig";
import { getAndDeleteArchivedPrizeDrawConfigs, getPrizeDrawConfigsWithAssociatedPrizeDraw, getPrizeDrawFromHubspot } from "./syncPrizeDrawConfigFromHubspot/helpers";
import { getPrizeDraw, updatePrizeDrawPrizeSlotBuffer } from "../../entities/prizeDraws";
import { getHubspotApiToken } from "../../services/hubspot/helpers";
import { updateHubspotPrizeConfigsWithUuid } from "../../services/hubspot/prizeDrawConfigs";

export const getTotalSlotsNumber = (users: number, buffer: number) => {
  return users * 2 + buffer;
};

export const generatePrizeSlotsArray = ({
  totalSlots,
  month,
  year,
  prizeDrawId,
}: {
  totalSlots: number;
  month: number;
  year: number;
  prizeDrawId: string;
}) => {
  const paddedMonth = month.toString().padStart(2, "0");
  const slots = [];
  for (let i = 0; i < totalSlots; i++) {
    // Pad the slot ID with leading zeros to 11 digits
    const slotId = i.toString().padStart(11, "0");

    //Priority is 1 as higher priority slots will be introduced separately for guaranteed prizes.
    slots.push({
      pk: `PRIZE_DRAW#${prizeDrawId}`,
      sk: `SLOT#${slotId}`,
      gsi1pk: `PRIZE_DRAW#${prizeDrawId}`,
      gsi1sk: `IS_CLAIMED#FALSE#PRIORITY#1#SLOT_ID#${slotId}`,
      rewardAmount: 0,
      year: year,
      month: paddedMonth,
      slotId: slotId,
      isClaimed: false,
      priority: 1,
    });
  }
  return slots;
};

type Prize = {
  rewardAmount: number;
  configId: string;
  maximumNumberOfWinners: number;
};

export const distributePrizesToSlots = (
  slots: PrizeSlot[],
  prizes: Prize[],
): PrizeSlot[] => {
  const slotsWithPrizes = [...slots];

  prizes.forEach((prize) => {
    let remainingWinners = prize.maximumNumberOfWinners;
    const availableSlots = [...slotsWithPrizes.keys()];

    while (remainingWinners > 0 && availableSlots.length > 0) {
      const randomIndex = crypto.randomInt(availableSlots.length);
      const slotIndex = availableSlots[randomIndex];

      slotsWithPrizes[slotIndex] = {
        ...slotsWithPrizes[slotIndex],
        rewardAmount: prize.rewardAmount,
      };

      availableSlots.splice(randomIndex, 1);
      remainingWinners--;
    }
  });

  return slotsWithPrizes;
};

const getRandomTimeWithinMonth = (year: number, month: string) => {
  const daysInMonth = new Date(year, parseInt(month), 0).getDate();
  const maxDays = Math.floor(daysInMonth * 0.75);
  const randomDay = crypto.randomInt(1, maxDays + 1);
  const randomHour = crypto.randomInt(24);
  const randomMinute = crypto.randomInt(60);

  const scheduledTime = new Date(
    year,
    parseInt(month) - 1,
    randomDay,
    randomHour,
    randomMinute,
  );
  return scheduledTime.toISOString().split(".")[0];
};

export const scheduleAddGuaranteedPrizeToSlots = async ({
  guaranteedPrizes,
  prizeDrawId,
  lengthOfSlots,
  year,
  month,
}: {
  guaranteedPrizes: Prize[];
  prizeDrawId: string;
  lengthOfSlots: number;
  year: number;
  month: string;
}) => {
  const addGuaranteedPrizeToSlotsLambdaArn = getEnvVariable(
    "ADD_GUARANTEED_PRIZE_TO_SLOTS_LAMBDA_ARN",
  );

  const scheduler = new SchedulerClient();

  let slotNumber = lengthOfSlots;
  for (const prize of guaranteedPrizes) {
    for (let i = 1; i <= prize.maximumNumberOfWinners; i++) {
      const scheduleName = `gp-pdId${prizeDrawId.slice(0,4)}-cfgId${prize.configId.slice(0,4)}-${i}`;
      const slotId = slotNumber.toString().padStart(11, "0");
      slotNumber++;

      const scheduleMessage = {
        prizeDrawId,
        slotId,
        year,
        month,
        rewardAmount: prize.rewardAmount,
        source: "addGuaranteedPrizeToSlots",
      };

      const formattedTime = getRandomTimeWithinMonth(year, month);

      const scheduleCommand = new CreateScheduleCommand({
        Name: scheduleName,
        ScheduleExpression: `at(${formattedTime})`,
        FlexibleTimeWindow: {
          Mode: "OFF",
        },
        Target: {
          Arn: addGuaranteedPrizeToSlotsLambdaArn,
          RoleArn: getEnvVariable(
            "ADD_GUARANTEED_PRIZE_TO_SLOTS_SCHEDULER_ROLE_ARN",
          ),
          Input: JSON.stringify(scheduleMessage),
        },
      });

      await scheduler.send(scheduleCommand);
    }
  }
};

const updatePrizeDraw = async (prizeDrawHubspotRecordId: string) => {
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });
    
    const prizeDraw = await getPrizeDrawFromHubspot(hubspotClient, prizeDrawHubspotRecordId);

    const prizeSlotBuffer = parseInt(prizeDraw.properties.prize_slot_buffer, 10);
    await updatePrizeDrawPrizeSlotBuffer({prizeDrawId: prizeDraw.properties.prize_draw_reference, prizeSlotBuffer});
}

const updatePrizeDrawConfigs = async () => {
  const hubspotApiToken = await getHubspotApiToken();
  const hubspotClient = new Client({ accessToken: hubspotApiToken });
  const lastPrizeDrawConfigSync = await getLastPrizeDrawConfigSync();

  const prizeDrawConfigs = await getPrizeDrawConfigsWithAssociatedPrizeDraw(hubspotClient, lastPrizeDrawConfigSync?.date);

  const newPrizeDrawConfigsWithoutUuid = prizeDrawConfigs.filter(config => {
    const createDate = new Date(config.hs_createdate);
    const lastSyncDate = lastPrizeDrawConfigSync ? new Date(lastPrizeDrawConfigSync.date) : new Date(0);
    return createDate >= lastSyncDate && config.cuid === null;
  });

  const newPrizeDrawConfigsWithUuid = newPrizeDrawConfigsWithoutUuid.map(config => ({
    ...config,
    cuid: uuidv4()
  }));

    //Include new configs with uuid as well as exisiting configs that have been updated
   const allPrizeDrawConfigs = prizeDrawConfigs.map(config => {
    const newConfig = newPrizeDrawConfigsWithUuid.find(newConfig => newConfig.hs_record_id === config.hs_record_id);
    return newConfig || config;
  });

  await updateHubspotPrizeConfigsWithUuid(hubspotClient, newPrizeDrawConfigsWithUuid);
  
  await updatePrizeDrawConfigsFromHubspot({configs: allPrizeDrawConfigs});

  await getAndDeleteArchivedPrizeDrawConfigs(hubspotClient, lastPrizeDrawConfigSync?.date);

  await updateLastPrizeDrawConfigSync();
}

export const syncPrizeDrawWithConfigsFromHubspot = async (prizeDrawId: string) => {
  const { hubspotRecordId } = await getPrizeDraw(prizeDrawId);

  await updatePrizeDraw(hubspotRecordId);
  await updatePrizeDrawConfigs();
}