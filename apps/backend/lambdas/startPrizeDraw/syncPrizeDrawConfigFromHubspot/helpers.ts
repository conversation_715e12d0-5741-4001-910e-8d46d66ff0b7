import { Client } from "@hubspot/api-client";
import { batchDeletePrizeDrawConfigs } from "../../../entities/prizeDrawConfig";
import { combinePrizeDrawConfigsWithPrizeDraw, createPrizeDrawConfigToAssociationsMap, fetchAssociatedPrizeDraws, fetchDeletedPrizeDrawConfigs, fetchPrizeDrawConfigs, fetchPrizeDrawDetails, HubspotPrizeDrawConfigWithPrizeDraw } from "../../../services/hubspot/prizeDrawConfigs";
import { getAllPrizeDrawHubspotRecordIds, HubspotPrizeDraw } from "../../../services/hubspot/prizeDraws";

export const getPrizeDrawConfigsWithAssociatedPrizeDraw = async (hubspotClient: Client, lastSyncDate: string | undefined) => {
    const response = await fetchPrizeDrawConfigs(hubspotClient, lastSyncDate);
    const prizeDrawConfigIds = response.results.map((prizeDrawConfig) => prizeDrawConfig.id);

    if(prizeDrawConfigIds.length === 0) {
      console.log("No prize draw configs to fetch");
      return [];
    }

    const prizeDrawAssociationsResponse = await fetchAssociatedPrizeDraws(
      hubspotClient,
      prizeDrawConfigIds,
    );
    const prizeDrawAssociationsMap =
      createPrizeDrawConfigToAssociationsMap(prizeDrawAssociationsResponse);
    const allPrizeDrawHubspotRecordIds = getAllPrizeDrawHubspotRecordIds(prizeDrawAssociationsResponse);
    const prizeDrawsMap = await fetchPrizeDrawDetails(hubspotClient, allPrizeDrawHubspotRecordIds);

    const prizeDrawConfigsWithPrizeDraw = combinePrizeDrawConfigsWithPrizeDraw(
      response.results,
      prizeDrawAssociationsMap,
      prizeDrawsMap,
    );

    const prizeDrawConfigs: HubspotPrizeDrawConfigWithPrizeDraw[] = prizeDrawConfigsWithPrizeDraw.map(
      (prizeDrawConfig) => prizeDrawConfig.properties as unknown as HubspotPrizeDrawConfigWithPrizeDraw,
    );
    
    return prizeDrawConfigs;
}

export const getAndDeleteArchivedPrizeDrawConfigs = async (hubspotClient: Client, lastSyncDate: string | undefined) => {
  const deletedConfigIds = (await fetchDeletedPrizeDrawConfigs(hubspotClient, lastSyncDate)).filter(id => id !== null);

  if (deletedConfigIds.length > 0) {
    console.log(`Deleting ${deletedConfigIds.length} configs from DynamoDB`);
    await batchDeletePrizeDrawConfigs(deletedConfigIds);
  }
}

export const getPrizeDrawFromHubspot = async (hubspotClient: Client, prizeDrawHubspotRecordId: string) => {
  const prizeDrawMap = await fetchPrizeDrawDetails(hubspotClient, [prizeDrawHubspotRecordId]);
  return prizeDrawMap.get(prizeDrawHubspotRecordId) as HubspotPrizeDraw;
}