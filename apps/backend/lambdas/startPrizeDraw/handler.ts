import { queryPrizeDrawConfigsByPrizeDraw } from "../../entities/prizeDrawConfig";
import { getPrizeDraw, putActivePrizeDrawPointer } from "../../entities/prizeDraws";
import { batchInsertPrizeSlots } from "../../entities/prizeSlots";
import { getNumberOfUsersWithPolicyActive } from "../../entities/user";
import { getEnvVariable } from "../../helpers/getEnvVariable";
import { distributePrizesToSlots, generatePrizeSlotsArray, getTotalSlotsNumber, scheduleAddGuaranteedPrizeToSlots, syncPrizeDrawWithConfigsFromHubspot } from "./helpers";

interface PrizeDrawEvent {
  prizeDrawId: string;
  source: string;
}

export const handler = async (event: PrizeDrawEvent) => {
  const prizeSlotTableName = getEnvVariable("PRIZE_SLOT_TABLE");
  const { prizeDrawId, source } = event;

  if (source !== "startPrizeDraw") {
    throw new Error("Invalid event source");
  }

  try {
    await syncPrizeDrawWithConfigsFromHubspot(prizeDrawId);
  } catch (error) {
    console.error("Error syncing prize draw configs from Hubspot:", error);
    throw error;
  }

  try {
    const { prizeSlotBuffer, startDate } = await getPrizeDraw(prizeDrawId);
    const prizeDrawConfigs = await queryPrizeDrawConfigsByPrizeDraw(prizeDrawId);


    const users = await getNumberOfUsersWithPolicyActive();

    const totalSlots = getTotalSlotsNumber(users, prizeSlotBuffer);

    const nonZeroIndexedMonth = new Date(startDate).getMonth() + 1;
    const year = new Date(startDate).getFullYear();

    const slots = generatePrizeSlotsArray({
      totalSlots,
      month: nonZeroIndexedMonth,
      year,
      prizeDrawId,
    });
  
    const prizes = prizeDrawConfigs.filter(config => config.prizeType === "NON_GUARANTEED");
  
    const guaranteedPrizes = prizeDrawConfigs.filter(config => config.prizeType === "GUARANTEED");
  
    const slotsWithPrizes = distributePrizesToSlots(slots, prizes);

    try {
      await batchInsertPrizeSlots(slotsWithPrizes, prizeSlotTableName);
      await scheduleAddGuaranteedPrizeToSlots({
        guaranteedPrizes,
        prizeDrawId,
        lengthOfSlots: slotsWithPrizes.length,
        year,
        month: nonZeroIndexedMonth.toString().padStart(2, "0"),
      });
    } catch (error) {
      console.error("Error creating prize slots:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({ message: "Error creating prize slots" }),
      };
    }

    await putActivePrizeDrawPointer(prizeDrawId);

    console.log(`Successfully started prize draw ${prizeDrawId}`);
    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Prize draw started successfully" }),
    };
  } catch (error) {
    console.error("Error starting prize draw:", error);
    throw error;
  }
};
