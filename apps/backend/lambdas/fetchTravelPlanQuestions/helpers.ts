import { Client } from '@hubspot/api-client';
import { fetchDeletedTravelPlanQuestions } from '../../services/hubspot/travelPlanQuestions';
import { batchDeleteTravelPlanQuestions } from '../../entities/travelPlanQuestions';

export const getAndDeleteArchivedTravelPlanQuestions = async (hubspotClient: Client, lastSyncDate: string | undefined) => {
  const deletedQuestionIds = (await fetchDeletedTravelPlanQuestions(hubspotClient, lastSyncDate)).filter(id => id !== null);

  if (deletedQuestionIds.length > 0) {
    console.log(`Deleting ${deletedQuestionIds.length} travel plan questions from DynamoDB`);
    await batchDeleteTravelPlanQuestions(deletedQuestionIds);
  }
}