import { Client } from '@hubspot/api-client';
import { v4 as uuidv4 } from "uuid";
import { 
  createObjectToAssociationsMap,
  fetchAssociatedObjects,
  fetchObjectDetails,
  getAllObjectIds,
  getHubspotApiToken 
} from '../../services/hubspot/helpers';
import { 
  combineTravelPlanQuestionWithAssoicatedTravelPlanFormCuid,
  fetchTravelPlanQuestionsFromHubspot,
  updateHubspotTravelPlanQuestionsWithUuid 
} from '../../services/hubspot/travelPlanQuestions';
import { getLastTravelPlanQuestionsSync, updateLastTravelPlanQuestionsSync } from '../../entities/lastTravelPlanQuestionsSync';
import { updateTravelPlanQuestionsFromHubspot } from '../../entities/travelPlanQuestions';
import { getAndDeleteArchivedTravelPlanQuestions } from './helpers';
import { getTravelPlanFormObjectId, getTravelPlanQuestionObjectId } from '../../services/hubspot/customObjectIds';
import { getEnvVariable } from '../../helpers/getEnvVariable';

const STAGE_NAME = getEnvVariable("STAGE_NAME");

export const handler = async () => {
  try {
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });

    const lastTravelPlanQuestionsSync = await getLastTravelPlanQuestionsSync();
    const travelPlanQuestions = (await fetchTravelPlanQuestionsFromHubspot(hubspotClient, lastTravelPlanQuestionsSync?.date)).results;

    const newTravelPlanQuestions = travelPlanQuestions.filter(question => {
      const createDate = new Date(question.hs_createdate);
      const lastSyncDate = lastTravelPlanQuestionsSync ? new Date(lastTravelPlanQuestionsSync.date) : new Date(0);
      return createDate >= lastSyncDate && question.travel_plan_question_cuid === null;
    });

    newTravelPlanQuestions.forEach(question => {
      question.travel_plan_question_cuid = uuidv4();
    });
    
    const allTravelPlanQuestions = travelPlanQuestions.map(question => {
      const newQuestion = newTravelPlanQuestions.find(newQuestion => newQuestion.hs_object_id === question.hs_object_id);
      return newQuestion || question;
    });

    const travelPlanQuestionIds = allTravelPlanQuestions.map(question => question.hs_object_id);

    const associatedTravelPlanForms = await fetchAssociatedObjects(
      hubspotClient,
      travelPlanQuestionIds,
      getTravelPlanQuestionObjectId(),
      getTravelPlanFormObjectId(),
    );

    const associatedFormIds = getAllObjectIds(associatedTravelPlanForms);

    const associatedFormsMap = await fetchObjectDetails(
      hubspotClient,
      associatedFormIds,
      getTravelPlanFormObjectId(),
      ["travel_plan_form_cuid"],
    );

    const objectAssociatedMap = createObjectToAssociationsMap(
      associatedTravelPlanForms,
    );

    const combinedTravelPlanQuestionObject = combineTravelPlanQuestionWithAssoicatedTravelPlanFormCuid(
      allTravelPlanQuestions,
      objectAssociatedMap,
      associatedFormsMap,
    );


    if (STAGE_NAME === "prod" || STAGE_NAME === "staging") {
      await updateHubspotTravelPlanQuestionsWithUuid(hubspotClient, newTravelPlanQuestions);
    } else {
      console.log("Travel Plan Question cuid not updated due to it being a local environment.");
    }
    

    await updateTravelPlanQuestionsFromHubspot({ questions: combinedTravelPlanQuestionObject });

    await getAndDeleteArchivedTravelPlanQuestions(hubspotClient, lastTravelPlanQuestionsSync?.date);
    
    await updateLastTravelPlanQuestionsSync();
  } catch (error) {
    console.error("Error syncing travel plan questions with Hubspot: ", error);
  };
};