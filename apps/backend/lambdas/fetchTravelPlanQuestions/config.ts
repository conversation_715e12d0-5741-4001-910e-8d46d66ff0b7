import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import { Effect, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';

interface FetchTravelPlanQuestionsLambdaProps {
  platformTable: Table;
  hubspotSecret: Secret;
  stageName: string;
}

export class FetchTravelPlanQuestionsLambda extends BaseLambda {
  constructor(scope: Construct, props: FetchTravelPlanQuestionsLambdaProps) {
    const { platformTable, hubspotSecret, stageName } = props;
    const { stage } = getStage(scope);
    const id = `${stage}FetchTravelPlanQuestionsLambda`;

    const policyStatements = [
      new PolicyStatement({
        actions: ["secretsmanager:GetSecretValue"],
        resources: [hubspotSecret.secretArn],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: [platformTable.tableArn, `${platformTable.tableArn}/*`],
        actions: [
          "dynamodb:UpdateItem",
          "dynamodb:PutItem",
          "dynamodb:Query",
          "dynamodb:GetItem",
          "dynamodb:DeleteItem",
          "dynamodb:BatchWriteItem"
        ],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        HUBSPOT_API_TOKEN_SECRET_NAME: hubspotSecret.secretName,
        STAGE_NAME: stageName
      },
      policyStatements,
    });
  }
}
