import { Construct } from "constructs";
import { Table } from "aws-cdk-lib/aws-dynamodb";
import { BaseLambda } from "../../constructs/base-lambda/config";
import { getStage } from "../../helpers/getStage";
import {
  PolicyStatement,
  Effect,
  Role,
  ServicePrincipal,
} from "aws-cdk-lib/aws-iam";
import { getCdkHandlerPath } from "../../helpers/getCdkHandlerPath";
import { Secret } from "aws-cdk-lib/aws-secretsmanager";

interface CreatePrizeDrawLambdaProps {
  platformTable: Table;
  startPrizeDrawLambda: BaseLambda;
  hubspotApiTokenSecret: Secret;
  stageName: string;
}

export class CreatePrizeDrawLambda extends BaseLambda {
  constructor(scope: Construct, props: CreatePrizeDrawLambdaProps) {
    const { platformTable, startPrizeDrawLambda, hubspotApiTokenSecret, stageName } =
      props;
    const { stage } = getStage(scope);
    const id = `${stage}CreatePrizeDrawLambda`;

    // Create the scheduler role with the correct trust relationship
    const startPrizeDrawSchedulerRole = new Role(
      scope,
      `${stage}StartPrizeDrawSchedulerRole`,
      {
        roleName: `${stage}-start-prize-draw-scheduler-role`,
        assumedBy: new ServicePrincipal("scheduler.amazonaws.com"),
      },
    );

    // Add permissions to the scheduler role to invoke the Lambda
    startPrizeDrawSchedulerRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ["lambda:InvokeFunction"],
        resources: [startPrizeDrawLambda.functionArn],
      }),
    );
    const startPrizeDrawSchedulerRoleArn = startPrizeDrawSchedulerRole.roleArn;

    const policyStatements = [
      new PolicyStatement({
        resources: [platformTable.tableArn, `${platformTable.tableArn}/*`],
        actions: ["dynamodb:PutItem"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: ["*"],
        actions: ["scheduler:CreateSchedule"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        resources: [startPrizeDrawSchedulerRoleArn],
        actions: ["iam:PassRole"],
        effect: Effect.ALLOW,
      }),
      new PolicyStatement({
        actions: ["secretsmanager:GetSecretValue"],
        resources: [hubspotApiTokenSecret.secretArn],
        effect: Effect.ALLOW,
      }),
    ];

    super(scope, id, {
      lambdaEntry: getCdkHandlerPath(__dirname, { fileName: "handler" }),
      environment: {
        PLATFORM_TABLE: platformTable.tableName,
        START_PRIZE_DRAW_LAMBDA_ARN: startPrizeDrawLambda.functionArn,
        START_PRIZE_DRAW_SCHEDULER_ROLE_ARN: startPrizeDrawSchedulerRoleArn,
        HUBSPOT_API_TOKEN_SECRET_NAME: hubspotApiTokenSecret.secretName,
        STAGE_NAME: stageName,
      },
      policyStatements,
      timeout: 300,
    });
  }
}
