import { addMonths, format, getMonth, getYear, parse } from "date-fns";
import { v4 as uuidv4 } from "uuid";
import {
  CreateScheduleCommand,
  SchedulerClient,
} from "@aws-sdk/client-scheduler";
import { getEnvVariable } from "../../helpers/getEnvVariable";
import {
  PrizeConfig,
  putPrizeDrawConfigs,
} from "../../entities/prizeDrawConfig";
import { Client } from "@hubspot/api-client";
import { getHubspotApiToken } from "../../services/hubspot/helpers";
import { batchInsertPrizeDrawConfigsToHubspot } from "../../services/hubspot/prizeDrawConfigs";

export const getNextMonth = () => {
  const currentDate = new Date();
  const nextMonth = addMonths(currentDate, 1);
  return {
    month: getMonth(nextMonth),
    year: getYear(nextMonth),
  };
};

export const scheduleStartOfPrizeDraw = async (
  prizeDrawId: string,
  prizeDrawStartDate: string,
) => {
  const startPrizeDrawLambdaArn = getEnvVariable("START_PRIZE_DRAW_LAMBDA_ARN");

  const scheduler = new SchedulerClient();
  const scheduleName = `start-prize-draw-${prizeDrawId}`;

  const scheduleMessage = {
    prizeDrawId,
    source: "startPrizeDraw",
  };

  console.log("prizeDrawStartDate", prizeDrawStartDate);

  const scheduleCommand = new CreateScheduleCommand({
    Name: scheduleName,
    ScheduleExpression: `at(${format(new Date(prizeDrawStartDate), "yyyy-MM-dd'T'HH:mm:ss")})`,
    // ScheduleExpression: `at(${format(new Date(Date.now() + (60000 * 5)), "yyyy-MM-dd'T'HH:mm:ss")})`, // Schedule once 5 minutes from now for testing
    FlexibleTimeWindow: {
      Mode: "OFF",
    },
    Target: {
      Arn: startPrizeDrawLambdaArn,
      RoleArn: getEnvVariable("START_PRIZE_DRAW_SCHEDULER_ROLE_ARN"),
      Input: JSON.stringify(scheduleMessage),
    },
  });

  await scheduler.send(scheduleCommand);
};

export const uploadPrizeConfigToHubspotAndDynamoDB = async ({
  prizeDrawId,
  prizeDrawHubspotId,
}: {
  prizeDrawId: string;
  prizeDrawHubspotId: string;
}) => {
  const hubspotApiToken = await getHubspotApiToken();
  const hubspotClient = new Client({ accessToken: hubspotApiToken });

  const defaultPrizes: PrizeConfig[] = [
    {
      rewardAmount: 1000,
      configId: uuidv4(),
      maximumNumberOfWinners: 3,
      prizeType: "NON_GUARANTEED",
      hubspotRecordId: undefined,
    },
    {
      rewardAmount: 100,
      configId: uuidv4(),
      maximumNumberOfWinners: 4,
      prizeType: "NON_GUARANTEED",
      hubspotRecordId: undefined,
    },
    {
      rewardAmount: 50000,
      configId: uuidv4(),
      maximumNumberOfWinners: 2,
      prizeType: "NON_GUARANTEED",
      hubspotRecordId: undefined,
    },
    {
      rewardAmount: 1000000,
      configId: uuidv4(),
      maximumNumberOfWinners: 1,
      prizeType: "GUARANTEED",
      hubspotRecordId: undefined,
    },
  ];

  const insertedPrizeDrawConfigs = await batchInsertPrizeDrawConfigsToHubspot({
    defaultPrizeConfigs: defaultPrizes,
    prizeDrawHubspotId,
    hubspotClient,
  });

  const defaultPrizesWithHubspotIds = defaultPrizes.map((prize) => {
    const matchingConfig = insertedPrizeDrawConfigs.find(
      (config) => config.id === prize.configId,
    );
    return {
      ...prize,
      hubspotRecordId: matchingConfig?.hubspotRecordId,
    };
  });

  await putPrizeDrawConfigs({
    prizeDrawId,
    configs: defaultPrizesWithHubspotIds,
  });
};

export const getMonthYearText = (date: string): string => {
  const parsedDate = parse(date, "yyyy-MM-dd", new Date());
  return format(parsedDate, "MMM yyyy");
};
