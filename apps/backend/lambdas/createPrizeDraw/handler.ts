import { APIGatewayProxyResult } from "aws-lambda";
import {
  getNextMonth,
  uploadPrizeConfigToHubspotAndDynamoDB,
  getMonthYearText,
  scheduleStartOfPrizeDraw,
} from "./helpers";
import { v4 as uuidv4 } from "uuid";
import { putPrizeDraw } from "../../entities/prizeDraws";
import { format, startOfMonth, endOfMonth } from "date-fns";
import { Client } from "@hubspot/api-client";
import { getHubspotApiToken } from "../../services/hubspot/helpers";
import { sendPrizeDrawToHubspot } from "../../services/hubspot/prizeDraws";

export const handler = async (): Promise<APIGatewayProxyResult> => {
  const prizeDrawId = uuidv4();

  const { month, year } = getNextMonth();

  try {
    const prizeDrawStartDate = format(
      startOfMonth(new Date(year, month)),
      "yyyy-MM-dd",
    );
    const prizeDrawEndDate = format(
      endOfMonth(new Date(year, month)),
      "yyyy-MM-dd",
    );
    const hubspotApiToken = await getHubspotApiToken();
    const hubspotClient = new Client({ accessToken: hubspotApiToken });
    const prizeDrawName = `${getMonthYearText(prizeDrawStartDate)}`
    const prizeDrawSlotBuffer = 100;
    const prizeDrawRecordId = await sendPrizeDrawToHubspot(hubspotClient, prizeDrawId, prizeDrawName, prizeDrawStartDate, prizeDrawEndDate, prizeDrawSlotBuffer);

    await putPrizeDraw({
      startDate: prizeDrawStartDate,
      endDate: prizeDrawEndDate,
      prizeDrawId,
      recordId: prizeDrawRecordId,
      prizeDrawName,
      prizeSlotBuffer: prizeDrawSlotBuffer,
    });

    await uploadPrizeConfigToHubspotAndDynamoDB({
      prizeDrawId,
      prizeDrawHubspotId: prizeDrawRecordId,
    });

    await scheduleStartOfPrizeDraw(prizeDrawId, prizeDrawStartDate);
  } catch (error) {
    console.error("Error creating prize draw or schedule:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error creating prize draw or schedule",
      }),
    };
  }

  return {
    statusCode: 200,
    body: JSON.stringify({
      message: `Prize draw created and scheduled: ${prizeDrawId}`,
    }),
  };
};
