import { Construct } from "constructs";

export const getStage = (
  scope: Construct,
): { isProd: boolean; stage: string } => {
  const rawStage = scope.node.tryGetContext("stage");
  const isProd = rawStage === "prod";

  if (!rawStage) {
    throw new Error(
      'Stage not specified. Please provide a stage using the context parameter, e.g., "cdk deploy -c stage=<stage-name>".',
    );
  }

  const stage = rawStage === "prod" ? "" : rawStage;

  return { isProd, stage };
};
