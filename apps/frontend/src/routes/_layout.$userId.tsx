import { createFileRoute } from '@tanstack/react-router';
import { DrawSection } from '../components/DrawSection/DrawSection';
import { getGameInfoQueryOptions, useGetGameInfo } from '../infra/useGetGameInfo';

export const Route = createFileRoute('/_layout/$userId')({
  component: () => <HomeComponent />,
  loader: ({ context: { queryClient }, params: { userId } }) =>
    queryClient.ensureQueryData(getGameInfoQueryOptions({ userId }))
});

function HomeComponent() {
  const userId = Route.useParams().userId;
  const { gameInfo } = useGetGameInfo({ userId });

  return <DrawSection gameState={gameInfo?.gameState} formId={gameInfo?.formId} />;
}
