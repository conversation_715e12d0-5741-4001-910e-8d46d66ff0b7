import { Outlet, createRootRouteWithContext } from '@tanstack/react-router';
import { lazy, Suspense } from 'react';
import { Header } from '../components/Header';
import { Footer } from '../components/Footer';
import { QueryClient } from '@tanstack/react-query';
import { UserNotFound } from '../infra/Errors/UserNotFound';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Dynamically import the devtools
const TanStackRouterDevtools = import.meta.env.DEV
  ? lazy(() =>
      import('@tanstack/router-devtools').then((res) => ({
        default: res.TanStackRouterDevtools
      }))
    )
  : () => null;

export const Route = createRootRouteWithContext<{ queryClient: QueryClient }>()({
  component: () => {
    return (
      <>
        <Header />
        <Outlet />
        <Footer />
        {import.meta.env.DEV && (
          <Suspense>
            <TanStackRouterDevtools position="bottom-right" />
            <ReactQueryDevtools buttonPosition="top-right" />
          </Suspense>
        )}
      </>
    );
  },
  notFoundComponent: UserNotFound
});
