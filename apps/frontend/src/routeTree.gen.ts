/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LayoutImport } from './routes/_layout'
import { Route as LayoutUserIdImport } from './routes/_layout.$userId'

// Create/Update Routes

const LayoutRoute = LayoutImport.update({
  id: '/_layout',
  getParentRoute: () => rootRoute,
} as any)

const LayoutUserIdRoute = LayoutUserIdImport.update({
  id: '/$userId',
  path: '/$userId',
  getParentRoute: () => LayoutRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_layout': {
      id: '/_layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof LayoutImport
      parentRoute: typeof rootRoute
    }
    '/_layout/$userId': {
      id: '/_layout/$userId'
      path: '/$userId'
      fullPath: '/$userId'
      preLoaderRoute: typeof LayoutUserIdImport
      parentRoute: typeof LayoutImport
    }
  }
}

// Create and export the route tree

interface LayoutRouteChildren {
  LayoutUserIdRoute: typeof LayoutUserIdRoute
}

const LayoutRouteChildren: LayoutRouteChildren = {
  LayoutUserIdRoute: LayoutUserIdRoute,
}

const LayoutRouteWithChildren =
  LayoutRoute._addFileChildren(LayoutRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof LayoutRouteWithChildren
  '/$userId': typeof LayoutUserIdRoute
}

export interface FileRoutesByTo {
  '': typeof LayoutRouteWithChildren
  '/$userId': typeof LayoutUserIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_layout': typeof LayoutRouteWithChildren
  '/_layout/$userId': typeof LayoutUserIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '' | '/$userId'
  fileRoutesByTo: FileRoutesByTo
  to: '' | '/$userId'
  id: '__root__' | '/_layout' | '/_layout/$userId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  LayoutRoute: typeof LayoutRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  LayoutRoute: LayoutRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_layout"
      ]
    },
    "/_layout": {
      "filePath": "_layout.tsx",
      "children": [
        "/_layout/$userId"
      ]
    },
    "/_layout/$userId": {
      "filePath": "_layout.$userId.tsx",
      "parent": "/_layout"
    }
  }
}
ROUTE_MANIFEST_END */
