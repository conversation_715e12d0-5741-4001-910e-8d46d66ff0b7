import { useAnimationFrame } from 'framer-motion';
import { useRef, useState, useCallback } from 'react';

type Params = {
  totalDistance: number;
  onSpinEnded?: () => void;
  isMediumScreen: boolean;
};

type SpinningState = 'slow_speed' | 'increasing' | 'fast_speed' | 'decreasing' | 'ended';

const SLOW_SPEED = 50; // px/frame
const FAST_SPEED_MOBILE = 2500; // px/frame
const FAST_SPEED_DESKTOP = 4500; // px/frame
const ACCELERATION_INTERVAL = 20; // px/frame^2
const MINIMUM_SPIN_DURATION = 3_000; // ms

export const useCarouselAnimation = ({ totalDistance, onSpinEnded, isMediumScreen }: Params) => {
  const positionRef = useRef(0);
  const carouselRef = useRef<HTMLDivElement>(null);
  const speedRef = useRef(50);
  const [spinState, setSpinState] = useState<SpinningState>('slow_speed');
  const [badgeResultsShown, setBadgeResultsShown] = useState(false);
  const lastSpinStartTime = useRef<number | null>(null);

  const FAST_SPEED = isMediumScreen ? FAST_SPEED_DESKTOP : FAST_SPEED_MOBILE;

  useAnimationFrame(
    useCallback(
      (_, delta) => {
        switch (spinState) {
          case 'slow_speed':
            speedRef.current = SLOW_SPEED;
            break;
          case 'increasing':
            speedRef.current = Math.min(FAST_SPEED, speedRef.current + ACCELERATION_INTERVAL);
            if (speedRef.current === FAST_SPEED) {
              setSpinState('fast_speed');
            }
            break;
          case 'fast_speed':
            speedRef.current = FAST_SPEED;
            break;
          case 'decreasing':
            // Slow down the deceleration rate when speed is lower
            const decelerationRate =
              speedRef.current < SLOW_SPEED * 2
                ? ACCELERATION_INTERVAL * 0.5
                : ACCELERATION_INTERVAL;
            speedRef.current = Math.max(0, speedRef.current - decelerationRate);
            if (speedRef.current === 0) {
              setSpinState('ended');
              setTimeout(() => {
                setBadgeResultsShown(true);
              }, 300);
              onSpinEnded?.();
            }
            break;
          case 'ended':
            speedRef.current = 0;
            break;
        }
        const distance = (speedRef.current / 1000) * delta;
        positionRef.current = (positionRef.current + distance) % totalDistance;

        if (carouselRef.current) {
          carouselRef.current.style.transform = `translateX(-${positionRef.current}px)`;
        }
      },
      [speedRef, spinState, setSpinState, onSpinEnded, FAST_SPEED]
    )
  );

  const startSpinningFaster = useCallback(() => {
    setSpinState('increasing');
    lastSpinStartTime.current = Date.now();
  }, [setSpinState]);

  const stopSpinning = useCallback(() => {
    if (!lastSpinStartTime.current) {
      setSpinState('decreasing');
      return;
    }

    if (Date.now() - lastSpinStartTime.current < MINIMUM_SPIN_DURATION) {
      const timeToWait = MINIMUM_SPIN_DURATION - (Date.now() - lastSpinStartTime.current);
      setTimeout(() => setSpinState('decreasing'), timeToWait);
      return;
    }

    setSpinState('decreasing');
  }, [setSpinState]);

  return { carouselRef, startSpinningFaster, stopSpinning, spinState, badgeResultsShown };
};
