import { useEffect, useState } from 'react';

export const useIsMediumScreen = () => {
  const [isMediumScreen, setIsMediumScreen] = useState(
    window.matchMedia('(min-width: 768px)').matches
  );

  useEffect(() => {
    const mediaQuery = window.matchMedia('(min-width: 768px)');
    const handleResize = (e: MediaQueryListEvent) => setIsMediumScreen(e.matches);

    mediaQuery.addEventListener('change', handleResize);
    return () => mediaQuery.removeEventListener('change', handleResize);
  }, []);

  return {
    isMediumScreen
  };
};
