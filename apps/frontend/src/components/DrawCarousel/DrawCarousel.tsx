import { useCallback, useEffect, useState } from 'react';
import carouselLuggage1 from '../../assets/carousel-luggage/1.webp';
import carouselLuggage2 from '../../assets/carousel-luggage/2.webp';
import carouselLuggage3 from '../../assets/carousel-luggage/3.webp';
import carouselLuggage4 from '../../assets/carousel-luggage/4.webp';
import carouselWinner from '../../assets/carousel-luggage/winner.webp';
import carouselLoser from '../../assets/carousel-luggage/loser.webp';
import ConfettiExplosion, { ConfettiProps } from 'react-confetti-explosion';
import { AnimatePresence, motion } from 'framer-motion';
import clsx from 'clsx';
import { PrizeDialog } from '../PrizeDialog';
import { AviosBadge } from '../AviosBadge';
import { useCarouselAnimation } from './useCarouselAnimation';
import { useIsMediumScreen } from './useIsMediumScreen';
import { formatPrizeAmountToShortString } from '../../utils/formatPrizeAmount';
import { useInitiateDrawPlay } from '../../infra/useInitiateDrawPlay';
import { useGetDrawPlayResult } from '../../infra/useGetDrawPlayResult';

const confettiProps: ConfettiProps = {
  force: 0.9,
  duration: 3000,
  particleCount: 350,
  width: 1600,
  zIndex: 1000,
  colors: ['#041E43', '#1471BF', '#5BB4DC', '#FC027B', '#66D805']
};

const images = [carouselLuggage1, carouselLuggage2, carouselLuggage3, carouselLuggage4];
const allImages = [...images, carouselWinner, carouselLoser];

export const DrawCarousel = () => {
  const { isMediumScreen } = useIsMediumScreen();
  const totalDistance = isMediumScreen ? 2312 : 1298;
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isConfettiAnimationFinished, setIsConfettiAnimationFinished] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  
  const onSpinEnded = useCallback(() => {
    setTimeout(() => {
      setDialogOpen(true);
    }, 2000);
  }, [setDialogOpen]);
  const { carouselRef, startSpinningFaster, stopSpinning, spinState, badgeResultsShown } =
    useCarouselAnimation({
      totalDistance,
      onSpinEnded,
      isMediumScreen
    });

  const { initiateDrawPlay, initiateDrawPlayResult } = useInitiateDrawPlay();
  const drawPlayId = initiateDrawPlayResult?.drawPlayId;
  const { amountWon, isLoading } = useGetDrawPlayResult({ drawPlayId, onSuccess: stopSpinning });
  const amountWonString = formatPrizeAmountToShortString(amountWon ?? 0);

  useEffect(() => {
    if (spinState === 'ended' && amountWon && amountWon > 0) {
      const timer = setTimeout(() => {
        setShowConfetti(true);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [spinState, amountWon]);

  const handleConfettiComplete = () => {
    setIsConfettiAnimationFinished(true);
  };

  const handleStartDraw = async () => {
    if (spinState !== 'slow_speed') return;
    initiateDrawPlay();
    startSpinningFaster();
  };

  // Preload all images in React
  for (const image of allImages) {
    const preloadedImage = new Image();
    preloadedImage.src = image;
  }

  return (
    <>
      <PrizeDialog
        open={dialogOpen && !isLoading}
        setOpen={setDialogOpen}
        amountWon={amountWon}
        drawPlayId={drawPlayId}
      />
      <div className="bg-black relative cursor-pointer group" onClick={handleStartDraw}>
        <div className="flex w-full justify-center pt-16 -mb-32 z-20">
          <AviosBadge
            className={clsx(
              'flex flex-col items-center relative duration-300 z-50',
              spinState === 'slow_speed' && 'group-hover:opacity-85',
              !badgeResultsShown && spinState !== 'slow_speed' && 'opacity-30'
            )}
            smallText={badgeResultsShown ? "You've won" : 'Tap to play'}
            largeText={badgeResultsShown ? amountWonString : '???'}
          />
        </div>
        <div className="absolute top-1/3 left-1/2 z-50">
          {showConfetti && !isConfettiAnimationFinished ? (
            <ConfettiExplosion {...confettiProps} onComplete={handleConfettiComplete} />
          ) : null}
        </div>
        <div className="overflow-hidden h-96 flex relative">
          <div className="w-full flex justify-center items-end pb-2 absolute bottom-0 z-40 bg-gradient-to-t from-black to-transparent h-24"></div>
          <AnimatePresence>
            {spinState !== 'ended' && (
              <motion.div
                ref={carouselRef}
                className={`flex gap-5 items-end`}
                exit={{ opacity: 0 }}>
                {[...images, ...images].map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt="Luggage"
                    className="h-44 md:h-80 w-auto max-w-none select-none"
                  />
                ))}
              </motion.div>
            )}
          </AnimatePresence>
          <AnimatePresence>
            {spinState === 'ended' && (
              <motion.div
                className={`w-full flex gap-5 items-end`}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, ease: 'easeOut' }}>
                <img
                  src={amountWon && amountWon > 0 ? carouselWinner : carouselLoser}
                  alt="Luggage"
                  className="h-64 md:h-80 w-auto max-w-none select-none mx-auto"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </>
  );
};
