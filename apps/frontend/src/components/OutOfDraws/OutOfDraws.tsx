import { useCountdown } from './useCountDown';
import loserImage from '../../assets/out-of-draws.webp';

export const OutOfDraws = () => {
  const startOfNextMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth() + 1,
    1
  ).toISOString();
  const { days, hours, minutes } = useCountdown(startOfNextMonth);

  return (
    <div className="bg-black py-10">
      <div className="flex justify-between md:flex-row">
        <div className="px-5 md:flex-1 md:flex md:flex-col md:justify-center md:px-12">
          <p className="text-wanda-white text-4xl md:text-6xl lg:text-8xl font-extrabold">
            You're out of plays for this month.
          </p>
          <div className="h-10" />
          <p className="text-wanda-white text-xl">The next Prize Draw opens in:</p>
          <div className="h-6" />
          <div className="flex gap-3">
            <div>
              <div className="rounded-lg bg-wanda-white w-24 h-16 flex justify-center items-center mb-3">
                <p className="text-xl">{days}</p>
              </div>
              <p className="text-center text-wanda-white text-lg">Days</p>
            </div>
            <div>
              <div className="rounded-lg bg-wanda-white w-24 h-16 flex justify-center items-center mb-3">
                <p className="text-xl">{hours}</p>
              </div>
              <p className="text-center text-wanda-white text-lg">Hrs</p>
            </div>
            <div>
              <div className="rounded-lg bg-wanda-white w-24 h-16 flex justify-center items-center mb-3">
                <p className="text-xl">{minutes}</p>
              </div>
              <p className="text-center text-wanda-white text-lg">Mins</p>
            </div>
          </div>
        </div>
        <div className="hidden md:block md:flex-1 w-full h-[38rem] relative">
          <img
            src={loserImage}
            alt="Luggage"
            className="absolute -right-32 h-full object-contain"
          />
        </div>
      </div>

      <div className="h-8" />
    </div>
  );
};
