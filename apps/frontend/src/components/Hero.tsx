import heroImage from '../assets/hero-image-desktop.webp';
import arrowDownImage from '../assets/arrow-down.webp';
import { HeroTemplate } from './HeroTemplate';

export const DRAW_SECTION_ANCHOR_ID = 'draw-section-anchor-id';

export const Hero = () => {
  const scrollToNextSection = () => {
    const element = document.getElementById(DRAW_SECTION_ANCHOR_ID);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  };

  return (
    <>
      <HeroTemplate
        image={heroImage}
        title={'Grab the Bag!'}
        description={'Click or tap on the luggage carousel below for your chance to win.'}
      />
      <div className="relative flex justify-center w-full">
        <img
          src={arrowDownImage}
          alt="Arrow down Image"
          className="z-10 absolute -translate-y-3/4 cursor-pointer hidden lg:block"
          onClick={scrollToNextSection}
        />
      </div>
    </>
  );
};
