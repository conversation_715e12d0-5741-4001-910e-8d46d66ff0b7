import { GameResponse } from '../../infra/GameState.type';
import { ConfirmTravelPlan } from '../ConfirmTravelPlan/ConfirmTravelPlan';
import { DrawCarousel } from '../DrawCarousel/DrawCarousel';
import { OutOfDraws } from '../OutOfDraws/OutOfDraws';

type Props = {
  gameState: GameResponse['gameState'];
  formId: GameResponse['formId'];
};

export const DrawSection = ({ gameState, formId }: Props) => {
  switch (gameState) {
    case 'PENDING_FIRST_PLAY':
    case 'PENDING_SECOND_PLAY':
      return <DrawCarousel />;
    case 'PENDING_CONFIRM_TRAVEL_PLANS':
      return <ConfirmTravelPlan formId={formId} />;
    case 'OUT_OF_DRAWS':
      return <OutOfDraws />;
    default:
      <div />;
  }
};
