import React, { memo } from 'react';

type Props = Omit<React.SVGProps<SVGSVGElement>, 'height' | 'width'> & {
  size?: number;
  color?: string;
  className?: string;
};

const ArrowRightIconSvg = ({ size = 24, color = '#F5F5F5', className, ...props }: Props) => (
  <svg width={size} height={size} viewBox="0 0 16 16" fill="none" {...props} className={className}>
    <path
      d="M3.33594 8.00016H12.6693M12.6693 8.00016L8.0026 3.3335M12.6693 8.00016L8.0026 12.6668"
      // stroke={color}
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowRightIcon = memo(ArrowRightIconSvg);
