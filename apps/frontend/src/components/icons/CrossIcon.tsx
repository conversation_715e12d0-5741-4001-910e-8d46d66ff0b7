import React, { memo } from 'react';

type Props = Omit<React.SVGProps<SVGSVGElement>, 'height' | 'width'> & {
  size?: number;
  color?: string;
};

const CrossIconSvg = ({ size = 24, color = '#252323', ...props }: Props) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
    <path
      d="M18 6L6 18M6 6L18 18"
      stroke={color}
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CrossIcon = memo(CrossIconSvg);
