import React from 'react';
import { Control } from 'react-hook-form';
import { TravelPlanQuestion, TravelPlanQuestions } from '../../infra/useGetTravelPlanQuestionsFromForm';
import { TextBoxInput } from './Inputs/TextBoxInput';
import { SingleDayPicker } from './Inputs/SingleDayPicker';
import { Combobox as CustomCombobox } from './Inputs/Combobox';
import { countryList } from './CountryList';
import { RangeDayPicker } from './Inputs/RangeDayPicker';

type Props = {
  travelPlanQuestions: TravelPlanQuestions;
  control: Control<any>;
};

export const TravelPlanFormInputs = ({ travelPlanQuestions, control }: Props) => {
  const renderQuestionInput = (question: TravelPlanQuestion) => {
    const commonProps = {
      label: question.question,
      placeholder: question.questionPlaceholderText,
      control: control,
      controllerName: `questions.${question.questionId}`
    };

    switch (question.questionFormat) {
      case 'Text Input':
        return <TextBoxInput {...commonProps} />;
      case 'Date Picker Range':
        return <RangeDayPicker {...commonProps} />;
      case 'Date Select Day':
        return <SingleDayPicker {...commonProps} />;
      case 'Combobox':
        return (
          <CustomCombobox
            {...commonProps}
            options={question.comboboxOptions ?? []}
            getDisplayValue={(value) => value ?? ''}
            getFilteredOptions={(query, options) => 
              options
                .filter((option) => 
                  option.toLowerCase().includes(query.toLowerCase())
                )
                .slice(0, 7)
            }
          />
        );
      default:
        return null;
    }
  };

  const formInputs = [...travelPlanQuestions.questions]
    .reverse()
    .map((question, index) => {
      const input = renderQuestionInput(question);
      const isLastItem = index === travelPlanQuestions.questions.length - 1;

      return (
        <React.Fragment key={question.questionId}>
          {input}
          {!isLastItem && <div className="h-4" />}
        </React.Fragment>
      );
    });

  return formInputs;
};