import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { TravelPlanQuestions } from '../../infra/useGetTravelPlanQuestionsFromForm';
import { TravelFormData } from './constants';
import { format } from 'date-fns';

interface UseTravelFormParams {
  onFormSubmit: (data: TravelFormData) => void;
  travelPlanQuestions: TravelPlanQuestions | undefined;
}

export const generateValidationSchema = (travelPlanQuestions: TravelPlanQuestions | undefined) => {
  if (!travelPlanQuestions) return z.object({ questions: z.object({}) });

  const questionFields: Record<string, z.ZodTypeAny> = {};

  travelPlanQuestions.questions.forEach((question) => {
    const fieldName = `${question.questionId}`;

    switch (question.questionFormat) {
      case 'Date Picker Range':
        // For date ranges, we'll store them in a temporary object and transform on submit
        questionFields[fieldName] = z.object({
          from: z.date(),
          to: z.date()
        }).refine((data) => data.from && data.to, {
          message: 'Both dates are required'
        }).transform(data => `${format(data.from, 'dd/MM/yyyy')} - ${format(data.to, 'dd/MM/yyyy')}`);
        break;
      case 'Date Select Day':
        questionFields[fieldName] = z.date({
          required_error: 'Date is required',
        }).transform(data => format(data, 'dd/MM/yyyy'));
        break;
      case 'Text Input':
      case 'Combobox':
        questionFields[fieldName] = z.string().min(1, 'This field is required');
        break;
      default:
        questionFields[fieldName] = z.string();
    }
  });

  return z.object({
    questions: z.object(questionFields)
  });
};

export const useTravelForm = ({ onFormSubmit, travelPlanQuestions }: UseTravelFormParams) => {
  const {
    control,
    handleSubmit,
    resetField,
    formState: { isValid }
  } = useForm<TravelFormData>({
    resolver: zodResolver(generateValidationSchema(travelPlanQuestions))
  });

  const onSubmit = (data: TravelFormData) => {
    onFormSubmit(data);
  };

  return {
    control,
    onSubmit: handleSubmit(onSubmit),
    isTravelFormValid: isValid,
    resetField
  };
};
