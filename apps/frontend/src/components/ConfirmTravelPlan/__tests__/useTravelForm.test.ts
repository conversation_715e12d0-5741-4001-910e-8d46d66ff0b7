import { format } from 'date-fns';
import { describe, expect, it } from 'vitest';
import { generateValidationSchema } from '../useTravelForm';
import { TravelPlanQuestions } from '../../../infra/useGetTravelPlanQuestionsFromForm';

describe('generateValidationSchema', () => {
  it('returns empty schema when travelPlanQuestions is undefined', () => {
    const schema = generateValidationSchema(undefined);
    expect(schema.shape).toEqual({
      questions: expect.any(Object)
    });
    
    // Should pass with empty questions object
    expect(() => schema.parse({ questions: {} })).not.toThrow();
  });

  it('validates Text Input questions correctly', () => {
    const questions: TravelPlanQuestions = {
      formTitle: 'Test Form',
      questions: [{
        question: 'What is your destination?',
        questionFormat: 'Text Input',
        questionId: 'q1',
      }]
    };

    const schema = generateValidationSchema(questions);
    
    // Should pass
    expect(() => schema.parse({
      questions: { q1: 'Paris' }
    })).not.toThrow();

    // Should fail
    expect(() => schema.parse({
      questions: { q1: '' }
    })).toThrow('This field is required');
  });

  it('validates Date Picker Range questions correctly', () => {
    const questions: TravelPlanQuestions = {
      formTitle: 'Test Form',
      questions: [{
        question: 'When are you traveling?',
        questionFormat: 'Date Picker Range',
        questionId: 'q1',
      }]
    };

    const schema = generateValidationSchema(questions);
    const testDate = new Date();
    const futureDate = new Date(testDate.getTime() + 86400000); // tomorrow

    // Should pass
    const validResult = schema.parse({
      questions: {
        q1: {
          from: testDate,
          to: futureDate
        }
      }
    }) as {
      questions: {
        q1: string;
      }
    };

    expect(validResult.questions.q1).toBe(
      `${format(testDate, 'dd/MM/yyyy')} - ${format(futureDate, 'dd/MM/yyyy')}`
    );

    // Should fail when dates are missing
    expect(() => schema.parse({
      questions: {
        q1: {
          from: null,
          to: null
        }
      }
    })).toThrow('Expected date, received null');

    // Should fail when dates are undefined
    expect(() => schema.parse({
      questions: {
        q1: {
          from: undefined,
          to: undefined
        }
      }
    })).toThrow('Required');
  });

  it('validates Date Select Day questions correctly', () => {
    const questions: TravelPlanQuestions = {
      formTitle: 'Test Form',
      questions: [{
        question: 'What day are you departing?',
        questionFormat: 'Date Select Day',
        questionId: 'q1',
      }]
    };

    const schema = generateValidationSchema(questions);
    const testDate = new Date();

    // Should pass
    const validResult = schema.parse({
      questions: {
        q1: testDate
      }
    }) as {
      questions: {
        q1: string;
      }
    };

    expect(validResult.questions.q1).toBe(format(testDate, 'dd/MM/yyyy'));

    // Should fail with the correct error message
    expect(() => schema.parse({
      questions: {
        q1: null
      }
    })).toThrow('Expected date, received null');
  });

  it('validates Combobox questions correctly', () => {
    const questions: TravelPlanQuestions = {
      formTitle: 'Test Form',
      questions: [{
        question: 'Select your preferred airline',
        questionFormat: 'Combobox',
        questionId: 'q1',
      }]
    };

    const schema = generateValidationSchema(questions);
    
    // Should pass
    expect(() => schema.parse({
      questions: { q1: 'British Airways' }
    })).not.toThrow();

    // Should fail
    expect(() => schema.parse({
      questions: { q1: '' }
    })).toThrow('This field is required');
  });

  it('validates multiple questions of different formats correctly', () => {
    const questions: TravelPlanQuestions = {
      formTitle: 'Test Form',
      questions: [
        {
          question: 'Where are you going?',
          questionFormat: 'Text Input',
          questionId: 'q1',
        },
        {
          question: 'When are you traveling?',
          questionFormat: 'Date Picker Range',
          questionId: 'q2',
        },
        {
          question: 'Select airline',
          questionFormat: 'Combobox',
          questionId: 'q3',
        }
      ]
    };

    const schema = generateValidationSchema(questions);
    const testDate = new Date();
    const futureDate = new Date(testDate.getTime() + 86400000);

    // Should pass with type assertion to help TypeScript understand the structure
    const validResult = schema.parse({
      questions: {
        q1: 'Paris',
        q2: {
          from: testDate,
          to: futureDate
        },
        q3: 'British Airways'
      }
    }) as {
      questions: {
        q1: string;
        q2: string;
        q3: string;
      }
    };

    // Remove console.log as it's not needed for the test
    expect(validResult.questions.q1).toBe('Paris');
    expect(validResult.questions.q2).toBe(
      `${format(testDate, 'dd/MM/yyyy')} - ${format(futureDate, 'dd/MM/yyyy')}`
    );
    expect(validResult.questions.q3).toBe('British Airways');

    // Should fail if any required field is missing
    expect(() => schema.parse({
      questions: {
        q1: 'Paris',
        q2: {
          from: testDate,
          to: futureDate
        },
        q3: '' // Invalid
      }
    })).toThrow('This field is required');
  });
});