import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TravelPlanFormInputs } from '../TravelPlanFormInputs';
import { useForm } from 'react-hook-form';
import { describe, it, expect } from 'vitest';

const FormWrapper = ({ defaultValues = {} }) => {
  const { control } = useForm({ defaultValues });
  const mockQuestions = {
    formTitle: 'Test Form',
    questions: [
      {
        questionId: 'destination',
        questionFormat: 'Text Input' as const,
        question: 'Where are you traveling to?',
        questionPlaceholderText: 'Enter destination',
    },
    {
      questionId: 'dateRange',
      questionFormat: 'Date Picker Range' as const,
      question: 'When are you traveling?',
      questionPlaceholderText: 'Select dates',
    },
    {
      questionId: 'country',
        questionFormat: 'Combobox' as const,
        question: 'Select country',
        questionPlaceholderText: 'Choose a country',
      }
    ]
  };

  return (
    <TravelPlanFormInputs
      travelPlanQuestions={mockQuestions}
      control={control}
    />
  );
};

describe('TravelPlanFormInputs', () => {
  it('renders all input types correctly', () => {
    render(<FormWrapper />);

    expect(screen.getByText('Where are you traveling to?')).toBeInTheDocument();
    expect(screen.getByText('When are you traveling?')).toBeInTheDocument();
    expect(screen.getByText('Select country')).toBeInTheDocument();
  });

  it('allows typing in text input', async () => {
    const user = userEvent.setup();
    render(<FormWrapper />);

    const textContainer = screen.getByText('Where are you traveling to?').closest('.flex.flex-col');
    const textInput = textContainer?.querySelector('input');
    expect(textInput).toBeInTheDocument();

    if (textInput) {
      await user.type(textInput, 'Paris');
      expect(textInput).toHaveValue('Paris');
    }
  });

  it('shows country suggestions in combobox when typing', async () => {
    const user = userEvent.setup();
    render(<FormWrapper />);

    const comboboxContainer = screen.getByText('Select country').closest('.flex.flex-col');
    const comboboxInput = comboboxContainer?.querySelector('input');
    expect(comboboxInput).toBeInTheDocument();

    if (comboboxInput) {
      await user.type(comboboxInput, 'can');
      expect(screen.getByText('Canada')).toBeInTheDocument();
    }
  });

  it('maintains proper spacing between inputs', () => {
    const { container } = render(<FormWrapper />);
    
    const spacers = container.getElementsByClassName('h-4');
    expect(spacers).toHaveLength(2);
  });
}); 