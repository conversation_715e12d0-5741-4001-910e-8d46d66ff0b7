import { Button } from '@headlessui/react';
import wantAnotherGoImage from '../../assets/want-another-go.webp';
import { useState } from 'react';
import { ConfirmTravelPlanFormDialog } from './ConfirmTravelPlanFormDialog';
import { useGetTravelPlanQuestionsForForm } from '../../infra/useGetTravelPlanQuestionsFromForm';
import { GameResponse } from '../../infra/GameState.type';
interface Props {
  formId: GameResponse['formId'];
}

export const ConfirmTravelPlan = ({ formId }: Props) => {
  const { travelPlanQuestions, isLoading: isTravelPlanQuestionsLoading } =
    useGetTravelPlanQuestionsForForm({ formId: formId });
  const [isConfirmTravelPlanFormDialogOpen, setIsConfirmTravelPlanFormDialogOpen] = useState(false);

  return (
    <>
      <ConfirmTravelPlanFormDialog
        isOpen={isConfirmTravelPlanFormDialogOpen}
        closeDialog={() => setIsConfirmTravelPlanFormDialogOpen(false)}
        travelPlanQuestions={travelPlanQuestions}
        isTravelPlanQuestionsLoading={isTravelPlanQuestionsLoading}
        formId={formId}
      />
      <div className="bg-black py-10">
        <div className="flex justify-between md:flex-row">
          <div className="px-5 md:flex-1 md:flex md:flex-col md:justify-center md:px-12">
            <p className="text-wanda-white text-4xl md:text-6xl lg:text-8xl font-extrabold">
              Want another go?
            </p>
            <div className="h-10" />

            <p className="text-wanda-white text-lg">
              Confirm your Travel Plans are up to date to get another play.
            </p>
            <div className="h-8" />
            <div>
              <Button
                className={
                  'bg-wanda-white hover:bg-surface-light transition-all duration-300 border border-wanda-black hover:border-border-primary hover:text-wanda-blue rounded-lg px-3 py-4'
                }
                onClick={() => setIsConfirmTravelPlanFormDialogOpen(true)}>
                <span className="font-medium text-xl">Confirm your Travel Plans</span>
              </Button>
            </div>
          </div>
          <div className="hidden md:block md:flex-1 w-full h-[38rem] relative overflow-hidden">
            <img
              src={wantAnotherGoImage}
              alt="Luggage"
              className="absolute scale-[1.6] xl:scale-150 translate-x-1/4 h-full object-contain 2xl:scale-110"
            />
          </div>
        </div>

        <div className="h-8" />
      </div>
    </>
  );
};
