import { Input } from '@headlessui/react';
import { fontSizes, fontWeights } from '../../../constants/fonts';
import { gaps } from '../../../constants/spacing';
import { Controller, Control, Path, PathValue } from 'react-hook-form';

interface TextBoxInputProps<T extends Record<string, any>> {
  label: string;
  placeholder: string | null;
  control: Control<T>;
  controllerName: Path<T>;
}

export const TextBoxInput = <T extends Record<string, any>>({
  label,
  placeholder,
  control,
  controllerName
}: TextBoxInputProps<T>) => {
  const placeholderText = placeholder ?? '';
  return (
    <Controller
      control={control}
      name={controllerName}
      defaultValue={'' as PathValue<T, Path<T>>}
      render={({ field: { value, onChange } }) => (
        <div className={`flex flex-col ${gaps.threexs} w-full`}>
          <p
            className={`${fontSizes.md} ${fontWeights.normal} leading-[22.4px] text-left text-text-darkest`}>
            {label}
          </p>
          <Input
            value={value}
            onChange={onChange}
            placeholder={placeholderText}
            className={`w-full rounded-[8px] px-4 py-3 ${fontSizes.md} bg-surface-light border border-border-primary ${fontWeights.normal} leading-4 text-left text-text-darkest placeholder:text-text-grey`}
          />
        </div>
      )}
    />
  );
};
