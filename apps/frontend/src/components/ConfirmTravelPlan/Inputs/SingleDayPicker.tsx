import { Input } from '@headlessui/react';
import { fontSizes, fontWeights } from '../../../constants/fonts';
import { gaps } from '../../../constants/spacing';
import { useState } from 'react';
import { DayPicker } from 'react-day-picker';
import { Controller, Control, Path } from 'react-hook-form';

interface SingleDayPickerProps<T extends Record<string, any>> {
  label: string;
  placeholder: string | null;
  control: Control<T>;
  controllerName: Path<T>;
}

export const SingleDayPicker = <T extends Record<string, any>>({
  label,
  placeholder,
  control,
  controllerName
}: SingleDayPickerProps<T>) => {
  const [isDayPickerOpen, setIsDayPickerOpen] = useState(false);
  const placeholderText = placeholder ?? '';
  const handleDayPickerClick = () => {
    setIsDayPickerOpen((previousIsDayPickerOpen) => !previousIsDayPickerOpen);
  };

  return (
    <Controller
      control={control}
      name={controllerName}
      render={({ field: { value, onChange } }) => (
        <div className={`flex flex-col ${gaps.threexs} w-full`}>
          <p
            className={`${fontSizes.md} ${fontWeights.normal} leading-[22.4px] text-left text-text-darkest`}>
            {label}
          </p>
          <Input
            onClick={handleDayPickerClick}
            value={value ? new Date(value).toLocaleDateString() : ''}
            placeholder={placeholderText}
            className={`w-full rounded-[8px] px-4 py-3 ${fontSizes.md} bg-surface-light border border-border-primary ${fontWeights.normal} leading-[16px] text-left text-text-darkest placeholder:text-text-grey cursor-pointer`}
            readOnly
          />
          {isDayPickerOpen && (
            <div className="relative w-full z-50">
              <div className="absolute right-0 left-0 rounded border-2 border-black -mt-[2px] bg-white px-2">
                <DayPicker
                  captionLayout="label"
                  showOutsideDays
                  mode="single"
                  required
                  selected={value ? new Date(value) : undefined}
                  disabled={{ before: new Date() }}
                  onSelect={(date) => {
                    onChange(date);
                    setIsDayPickerOpen(false);
                  }}
                />
              </div>
            </div>
          )}
        </div>
      )}
    />
  );
};
