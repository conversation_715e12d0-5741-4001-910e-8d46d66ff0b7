import { Input } from '@headlessui/react';
import { fontSizes, fontWeights } from '../../../constants/fonts';
import { gaps } from '../../../constants/spacing';
import { useState } from 'react';
import { DayPicker } from 'react-day-picker';
import { Controller, Control } from 'react-hook-form';

interface RangeDayPickerProps {
  label: string;
  placeholder: string | null;
  control: Control<any>;
  controllerName: string;
}

export const RangeDayPicker = ({
  label,
  placeholder,
  control,
  controllerName
}: RangeDayPickerProps) => {
  const [isDayPickerOpen, setIsDayPickerOpen] = useState(false);
  const placeholderText = placeholder ?? '';
  const handleDayPickerClick = () => {
    setIsDayPickerOpen((previousIsDayPickerOpen) => !previousIsDayPickerOpen);
  };

  return (
    <Controller
      control={control}
      name={controllerName}
      render={({ field: { value, onChange } }) => {
        const selectedDateRangeString = value
          ? `${value.from?.toLocaleDateString()} - ${value.to?.toLocaleDateString() ?? ''}`
          : '';

        return (
          <div className={`flex flex-col ${gaps.threexs} w-full`}>
            <p
              className={`${fontSizes.md} ${fontWeights.normal} leading-[22.4px] text-left text-text-darkest`}>
              {label}
            </p>
            <Input
              onClick={handleDayPickerClick}
              value={selectedDateRangeString}
              placeholder={placeholderText}
              className={`w-full rounded-[8px] px-4 py-3 ${fontSizes.md} bg-surface-light border border-border-primary ${fontWeights.normal} leading-[16px] text-left text-text-darkest placeholder:text-text-grey cursor-pointer`}
              readOnly
            />
            {isDayPickerOpen && (
              <div className="relative w-full z-50">
                <div className="absolute right-0 left-0 rounded border-2 border-black -mt-[2px] bg-white px-2">
                  <DayPicker
                    captionLayout="label"
                    showOutsideDays
                    mode="range"
                    required
                    disabled={{ before: new Date() }}
                    min={1}
                    onSelect={(dateRange) => {
                      onChange(dateRange);
                      if (dateRange?.to && dateRange?.from) {
                        setIsDayPickerOpen(false);
                      }
                    }}
                    selected={value}
                  />
                </div>
              </div>
            )}
          </div>
        );
      }}
    />
  );
};
