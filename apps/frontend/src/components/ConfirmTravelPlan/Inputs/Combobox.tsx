import {
  Combobox as HeadlessCombobox,
  ComboboxInput,
  ComboboxOptions,
  ComboboxOption,
  ComboboxButton
} from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { useState } from 'react';
import { Control, Controller } from 'react-hook-form';
import { fontSizes, fontWeights } from '../../../constants/fonts';
import { gaps } from '../../../constants/spacing';

interface ComboboxProps<T> {
  label: string;
  placeholder: string | null;
  control: Control<any>;
  controllerName: string;
  options: T[];
  getDisplayValue: (item: T) => string;
  getFilteredOptions: (query: string, options: T[]) => T[];
}

export const Combobox = <T,>({
  label,
  placeholder,
  control,
  controllerName,
  options,
  getDisplayValue,
  getFilteredOptions
}: ComboboxProps<T>) => {
  const [query, setQuery] = useState('');
  const filteredOptions = query === '' ? options : getFilteredOptions(query, options);
  const placeholderText = placeholder ?? 'Select an option';
  return (
    <Controller
      control={control}
      name={controllerName}
      render={({ field: { onChange, value } }) => (
        <div className={`flex flex-col ${gaps.threexs} w-full`}>
          <p
            className={`${fontSizes.md} ${fontWeights.normal} leading-[22.4px] text-left text-text-darkest`}>
            {label}
          </p>
          <HeadlessCombobox<T>
            value={value ?? null}
            onChange={onChange}
            onClose={() => setQuery('')}>
            <div className="relative">
              <ComboboxInput<T>
                displayValue={getDisplayValue}
                placeholder={placeholderText}
                className={`w-full rounded-[8px] px-4 py-3 ${fontSizes.md} bg-surface-light border border-border-primary ${fontWeights.normal} leading-[16px] text-left text-text-darkest placeholder:text-text-grey pr-10`}
                onChange={(event) => setQuery(event.target.value)}
              />
              <ComboboxButton className="absolute inset-y-0 right-0 flex items-center px-3">
                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
              </ComboboxButton>

              <div className="relative">
                <ComboboxOptions className="absolute z-10 w-full mt-1">
                  <div className="bg-white rounded-[8px] overflow-auto [border:1px_solid_rgba(217,217,217,1)] [box-shadow:0_1px_4px_0_rgba(0,0,0,0.1)] hover:[box-shadow:0_1px_4px_0_rgba(0,0,0,0.2)] max-h-[128px] min-w-[240px] p-2 gap-2">
                    {filteredOptions.map((option, index) => (
                      <ComboboxOption
                        key={index}
                        value={option}
                        className={`cursor-pointer px-4 py-3 ${fontSizes.sm} ${fontWeights.normal} leading-[16px] hover:bg-surface-light ui-active:bg-surface-light ui-active:text-text-darkest focus:outline-none`}>
                        {getDisplayValue(option)}
                      </ComboboxOption>
                    ))}
                  </div>
                </ComboboxOptions>
              </div>
            </div>
          </HeadlessCombobox>
        </div>
      )}
    />
  );
};
