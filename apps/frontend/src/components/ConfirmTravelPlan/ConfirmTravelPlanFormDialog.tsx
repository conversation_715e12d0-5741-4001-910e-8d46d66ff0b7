import { Button, <PERSON>alog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react';
import { CrossIcon } from '../icons/CrossIcon';
import 'react-day-picker/style.css';
import { useTravelForm } from './useTravelForm';
import { useSubmitTravelForm } from '../../infra/useSubmitTravelForm';
import { TravelPlanQuestions } from '../../infra/useGetTravelPlanQuestionsFromForm';
import { GameResponse } from '../../infra/GameState.type';
import { TravelPlanFormInputs } from './TravelPlanFormInputs';

type Props = {
  isOpen: boolean;
  closeDialog: () => void;
  travelPlanQuestions: TravelPlanQuestions | undefined;
  isTravelPlanQuestionsLoading: boolean;
  formId: GameResponse['formId'];
};

export const ConfirmTravelPlanFormDialog = ({
  isOpen,
  closeDialog,
  travelPlanQuestions,
  isTravelPlanQuestionsLoading,
  formId
}: Props) => {
  const { submitTravelForm, isTravelFormSubmitting } = useSubmitTravelForm(formId);
  const { control, isTravelFormValid, onSubmit } = useTravelForm({
    onFormSubmit: submitTravelForm,
    travelPlanQuestions
  });

  const formTitle = travelPlanQuestions?.formTitle || 'Confirm your Travel Plans';

  return (
    <Dialog open={isOpen} onClose={closeDialog} className="relative z-50">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-black/75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />
      <div className="fixed inset-0 z-50 w-screen">
        <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
          <DialogPanel
            transition
            className="relative transform rounded-lg border border-[#d9d9d9] bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-sm sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95">
            {isTravelPlanQuestionsLoading ? (
              <div>Loading...</div>
            ) : (
              <form
                name="ConfirmTravelPlans"
                className="flex flex-col items-start"
                onSubmit={onSubmit}>
                <div className="flex flex-row items-center w-full">
                  <DialogTitle as="h3" className="text-2xl font-semibold text-gray-900 text-center">
                    {formTitle}
                  </DialogTitle>
                  <div className="flex-1" />
                  <CrossIcon className="cursor-pointer" onClick={closeDialog} />
                </div>
                <div className="h-8" />

                {travelPlanQuestions && (
                  <TravelPlanFormInputs
                    travelPlanQuestions={travelPlanQuestions}
                    control={control}
                  />
                )}

                <div className="h-12 grow" />

                <div className="flex justify-end w-full">
                  <Button
                    type="submit"
                    disabled={!isTravelFormValid || isTravelFormSubmitting}
                    className={
                      'bg-wanda-blue rounded-lg p-3 text-white cursor-pointer hover:bg-wanda-blue-dark disabled:opacity-50 disabled:cursor-not-allowed text-base leading-4 transition-colors duration-300'
                    }>
                    Get my chance to win
                  </Button>
                </div>
              </form>
            )}
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
};
