import clsx from 'clsx';

type Props = {
  image: string;
  title: string;
  description: string;
  isTitleSmaller?: boolean;
  children?: React.ReactNode;
};

export const HeroTemplate = ({
  image,
  title,
  description,
  children,
  isTitleSmaller = false
}: Props) => {
  return (
    <div className="bg-wanda-blue">
      <div className="mx-auto flex flex-col md:flex-row max-w-7xl items-center justify-between p-6 md:pb-12 lg:px-8">
        <div className="lg:flex-1 md:w-1/2">
          <img
            src={image}
            alt="Hero Image"
            className="max-w-[450px] md:max-w-max w-full md:w-[115%]"
          />
        </div>
        <div className="max-w-[450px] md:max-w-full w-full md:w-1/2 flex lg:flex-1 flex-col justify-center lg:gap-10 gap-6 -mt-36 md:mt-0 items-start">
          <div className="md:h-8" /> {/* Spacer to lower the texts */}
          <h1
            className={clsx(
              'font-black text-wanda-cream tracking-tight ',
              isTitleSmaller
                ? 'xl:text-8xl lg:text-7xl md:text-6xl text-5xl'
                : 'lg:text-9xl text-8xl '
            )}>
            {title}
          </h1>
          <p className="lg:text-xl text-lg text-wanda-cream">{description}</p>
          {children}
        </div>
      </div>
    </div>
  );
};
