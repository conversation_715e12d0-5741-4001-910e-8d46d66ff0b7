import { Dialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react';
import { AviosBadge } from './AviosBadge';
import { CrossIcon } from './icons/CrossIcon';
import {
  formatPrizeAmountToShortString,
  formatPrizeAmountToString
} from '../utils/formatPrizeAmount';
import { useQueryClient } from '@tanstack/react-query';
import { GET_GAME_INFO_QUERY_QUEY } from '../infra/useGetGameInfo';
import sadMetallicEmoji from '../assets/sad-metallic-emoji.webp';

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  amountWon: number | undefined;
  drawPlayId: string | undefined;
};

export function PrizeDialog({ open, setOpen, amountWon, drawPlayId }: Props) {
  const queryClient = useQueryClient();

  // Preloading image in React
  const preloadedImage = new Image();
  preloadedImage.src = sadMetallicEmoji;

  const closeDialog = () => {
    setOpen(false);
    queryClient.invalidateQueries({ queryKey: [GET_GAME_INFO_QUERY_QUEY] });
  };

  const hasWon = amountWon !== undefined && amountWon > 0;
  const title = hasWon
    ? amountWon === 1_000_000
      ? 'You’re an Avios millionaire!'
      : 'You have won a prize!'
    : 'Not a winner this time';
  const description = hasWon
    ? `${formatPrizeAmountToString(amountWon ?? 0)} Avios are being added to your BA Exec Club Account.`
    : 'There’s 1 million Avios up for grabs this month. Better luck next time!';

  const illustration = hasWon ? (
    <AviosBadge
      className="flex flex-col items-center relative"
      smallText="You've won"
      largeText={formatPrizeAmountToShortString(amountWon ?? 0)}
    />
  ) : (
    <img src={sadMetallicEmoji} className="w-1/2 m-auto" />
  );

  return (
    <Dialog open={open} onClose={closeDialog} className="relative z-50">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-500/75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-50 w-screen overflow-y-auto">
        <div className="flex min-h-full justify-center p-4 items-center sm:p-0">
          <DialogPanel
            transition
            className="relative transform overflow-hidden rounded-lg bg-white p-8 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-sm sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95">
            <div className="absolute right-3 top-3">
              <CrossIcon className="cursor-pointer" onClick={closeDialog} />
            </div>

            <div>
              <DialogTitle as="h3" className="text-2xl font-semibold text-gray-900">
                {title}
              </DialogTitle>
              <div className="mt-6">
                {illustration}
                <p className="text-base mt-6">{description}</p>
                {drawPlayId && <p className="text-sm mt-6">Game play reference: {drawPlayId}</p>}
              </div>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
}
