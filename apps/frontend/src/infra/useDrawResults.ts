import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';
import { baseUrl } from './constants';

const drawResponseSchema = z.object({
  pointsWon: z.number(),
  gamePlayReferenceId: z.string()
});

export type DrawResponse = z.infer<typeof drawResponseSchema>;

const submitDraw = async (userId: string): Promise<unknown> => {
  const url = `${baseUrl}/draw`;

  const response = await fetch(url, {
    method: 'POST',
    body: JSON.stringify({ userId })
  });

  if (!response.ok) {
    throw new Error('Failed to submit draw');
  }

  return response.json();
};

const adaptDrawResponse = (data: unknown): DrawResponse => {
  return drawResponseSchema.parse(data);
};

export const useDrawResults = () => {
  return useMutation({
    mutationFn: async (userId: string) => adaptDrawResponse(await submitDraw(userId))
  });
};
