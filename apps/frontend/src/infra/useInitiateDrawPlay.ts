import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';
import { baseUrl } from './constants';
import { useParams } from '@tanstack/react-router';
const initiateDrawPlayResponseSchema = z.object({
  message: z.string(),
  drawPlayId: z.string(),
  drawId: z.string()
});

export type InitiateDrawPlayResponse = z.infer<typeof initiateDrawPlayResponseSchema>;

const initiateDrawPlay = async (userId: string): Promise<unknown> => {
  const url = `${baseUrl}/initiatePrizeDrawPlay`;

  const response = await fetch(url, {
    method: 'POST',
    body: JSON.stringify({ userId }),
    headers: {
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error('Failed to initate draw play');
  }

  return response.json();
};

const adaptInitiateDrawPlayResponse = (data: unknown): InitiateDrawPlayResponse => {
  return initiateDrawPlayResponseSchema.parse(data);
};

export const useInitiateDrawPlay = () => {
  const { userId } = useParams({ from: '/_layout/$userId' });

  const { mutateAsync, data } = useMutation({
    mutationKey: ['initiateDrawPlay', userId],
    mutationFn: async () => {
      if (!userId) {
        throw new Error('Missing userId');
      }
      return adaptInitiateDrawPlayResponse(await initiateDrawPlay(userId));
    },
    onError: (error) => {
      console.log("error", error);
    },
    throwOnError: true,
  });
  return { initiateDrawPlay: mutateAsync, initiateDrawPlayResult: data };
};
