import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';
import { baseUrl } from './constants';
import { TravelFormData } from '../components/ConfirmTravelPlan/constants';
import { useParams } from '@tanstack/react-router';
import { queryClient } from './queryClient';
import { GET_GAME_INFO_QUERY_QUEY } from './useGetGameInfo';
import { GameResponse } from './GameState.type';

const travelFormSubmitPayloadSchema = z.object({
  userId: z.string(),
  formId: z.string(),
  questionsWithAnswers: z.array(z.object({
    questionId: z.string(),
    answer: z.string()
  }))
});

export type TravelFormSubmitPayload = z.infer<typeof travelFormSubmitPayloadSchema>;

const submitTravelForm = async (
  travelFormSubmitPayload: TravelFormSubmitPayload
): Promise<unknown> => {
  const url = `${baseUrl}/submitTravelPlans`;

  const response = await fetch(url, {
    method: 'POST',
    body: JSON.stringify(travelFormSubmitPayload)
  });

  if (!response.ok) {
    throw new Error('Failed to submit travel form');
  }

  return response.text();
};

const adaptTravelFormResponse = ({
  travelFormData,
  formId,
  userId,
}: {
  travelFormData: TravelFormData,
  formId: GameResponse['formId'],
  userId?: string,
}): TravelFormSubmitPayload => {
  if (!userId) {
    throw new Error('User ID is required');
  }

  if (!formId) {
    throw new Error('Form ID is required');
  }

  const { questions } = travelFormData;
  
  const questionsWithAnswersArray = Object.entries(questions).map(([questionId, answer]) => ({
    questionId,
    answer
  }));

  return {
    userId,
    formId,
    questionsWithAnswers: questionsWithAnswersArray
  };
};

export const useSubmitTravelForm = (formId: GameResponse['formId']) => {
  const { userId } = useParams({ from: '/_layout/$userId' });

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ['submitTravelForm', userId],
    mutationFn: async (data: TravelFormData) => submitTravelForm(adaptTravelFormResponse({ travelFormData: data, userId, formId })),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [GET_GAME_INFO_QUERY_QUEY],
        refetchType: 'active'
      });
    }
  });

  return { submitTravelForm: mutateAsync, isTravelFormSubmitting: isPending };
};
