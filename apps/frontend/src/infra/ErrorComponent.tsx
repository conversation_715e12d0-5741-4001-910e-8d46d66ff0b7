import { useQueryErrorResetBoundary } from '@tanstack/react-query';
import { useRouter, ErrorComponentProps } from '@tanstack/react-router';
import { useEffect } from 'react';
import {
  NoAviosAccountLinkedError,
  PrizeDrawNotActiveError,
  UserNotFoundError,
  WandaPolicyInactiveError
} from './useGetGameInfo';
import { UserNotFound } from './Errors/UserNotFound';
import { NoAviosAccountLinked } from './Errors/NoAviosAccountLinked';
import { HeroTemplate } from '../components/HeroTemplate';
import { Button } from '@headlessui/react';
import { ArrowRightIcon } from '../components/icons/ArrowRightIcon';
import oopsImg from '../assets/oops.webp';
import { WandaPolicyInactive } from './Errors/WandaPolicyInactive';
import { PrizeDrawNotActive } from './Errors/PrizeDrawNotActive';

export const ErrorComponent = ({ error }: ErrorComponentProps) => {
  const router = useRouter();
  const queryErrorResetBoundary = useQueryErrorResetBoundary();

  useEffect(() => {
    queryErrorResetBoundary.reset();
  }, [queryErrorResetBoundary]);

  if (error instanceof UserNotFoundError) {
    return <UserNotFound />;
  }

  if (error instanceof PrizeDrawNotActiveError) {
    return <PrizeDrawNotActive />;
  }

  if (error instanceof NoAviosAccountLinkedError) {
    return <NoAviosAccountLinked />;
  }

  if (error instanceof WandaPolicyInactiveError) {
    return <WandaPolicyInactive />;
  }

  return (
    <HeroTemplate image={oopsImg} title={'Oops!'} description={'There was a problem.'}>
      <Button
        className={
          'group bg-wanda-black hover:bg-wanda-white hover:text-wanda-black disabled:bg-wanda-white disabled:text-wanda-black disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-4 rounded-lg flex flex-row items-center gap-2 cursor-pointer transition-colors duration-300'
        }
        onClick={() => window.location.reload()}>
        <span>Try again</span>
        <ArrowRightIcon size={16} className="group-hover:stroke-wanda-black stroke-wanda-white" />
      </Button>
    </HeroTemplate>
  );
};
