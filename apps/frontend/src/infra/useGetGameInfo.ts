import { queryOptions, useSuspenseQuery } from '@tanstack/react-query';
import { baseUrl } from './constants';
import { gameResponseSchema } from './GameState.type';

export const GET_GAME_INFO_QUERY_QUEY = 'gameInfo';

export class UserNotFoundError extends Error {}
export class NoAviosAccountLinkedError extends Error {}
export class WandaPolicyInactiveError extends Error {}
export class PrizeDrawNotActiveError extends Error {}

const fetchGameInfo = async (userId?: string) => {
  if (!userId) {
    throw new Error('User ID is required');
  }
  const url = `${baseUrl}/games`;
  const response = await fetch(`${url}?userId=${userId}`, {
    method: 'GET'
  });

  if (response.status === 404) {
    throw new UserNotFoundError('User not found');
  }

  if (!response.ok) {
    throw new Error('Failed to fetch game info');
  }

  const data = await response.json();
  const gameResponse = gameResponseSchema.parse(data);

  if (gameResponse.accountState === 'WANDA_POLICY_INACTIVE') {
    throw new WandaPolicyInactiveError();
  }

  if (gameResponse.accountState === 'NO_AVIOS_ACCOUNT_LINKED') {
    throw new NoAviosAccountLinkedError('No Avios account linked');
  }

  if (gameResponse.accountState === 'PRIZE_DRAW_NOT_ACTIVE') {
    throw new PrizeDrawNotActiveError();
  }

  return gameResponse;
};

type Params = {
  userId: string;
};

export const getGameInfoQueryOptions = ({ userId }: Params) =>
  queryOptions({
    queryKey: [GET_GAME_INFO_QUERY_QUEY, userId],
    queryFn: async () => fetchGameInfo(userId)
  });

export const useGetGameInfo = ({ userId }: Params) => {
  const { data, isLoading } = useSuspenseQuery(getGameInfoQueryOptions({ userId }));

  return { gameInfo: data, isLoading };
};
