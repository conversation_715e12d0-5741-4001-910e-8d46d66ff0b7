import { z } from 'zod';

export const gameResponseSchema = z.object({
  gameState: z.enum([
    'PENDING_FIRST_PLAY',
    'PENDING_CONFIRM_TRAVEL_PLANS',
    'PENDING_SECOND_PLAY',
    'OUT_OF_DRAWS'
  ]),
  accountState: z.enum(['VALID', 'NO_AVIOS_ACCOUNT_LINKED', 'WANDA_POLICY_INACTIVE', 'PRIZE_DRAW_NOT_ACTIVE']),
  formId: z.string().optional(),
});

export type GameResponse = z.infer<typeof gameResponseSchema>;
