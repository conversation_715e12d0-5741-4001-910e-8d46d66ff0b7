import type { DefaultOptions } from '@tanstack/react-query';
import { QueryClient } from '@tanstack/react-query';

export const createQueryClient = () => {
  const queryClient = new QueryClient({ defaultOptions });

  return queryClient;
};

// Required reading when choosing options: https://react-query.tanstack.com/guides/important-defaults

const defaultOptions: DefaultOptions = {
  queries: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true, // This works with the event listener above
    refetchOnMount: true, // Data will get refetched (with previous data still available) on mount if it's stale
    retryOnMount: true, // Data will get refetched (with hard-loading state) on mount if it errored the last time
    retry: false, // Without this, queries that error would be retried 3 times automatically
    throwOnError: (error, query) => typeof query.state.data === 'undefined'
  },
  mutations: {
    retry: false // Without this, mutations that error would be retried 3 times automatically
  }
};

export const queryClient = createQueryClient();
