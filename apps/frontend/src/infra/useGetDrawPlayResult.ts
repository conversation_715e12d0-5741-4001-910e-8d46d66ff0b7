import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { baseUrl } from './constants';
import { useParams } from '@tanstack/react-router';

const drawResponseSchema = z.object({
  pointsWon: z.number().optional(),
  isPending: z.boolean()
});

export type DrawResponse = z.infer<typeof drawResponseSchema>;

const getDrawPlayResult = async (userId: string, drawPlayId?: string): Promise<unknown> => {
  const url = `${baseUrl}/getPrizeDrawPlayResult?userId=${userId}&prizeDrawPlayId=${drawPlayId}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};

const adaptDrawResponse = (data: unknown): DrawResponse => {
  return drawResponseSchema.parse(data);
};

type Params = {
  drawPlayId?: string;
  onSuccess?: () => void;
};

export const useGetDrawPlayResult = ({ drawPlayId, onSuccess }: Params) => {
  const userId = useParams({ from: '/_layout/$userId' }).userId;

  const { data, isLoading, error } = useQuery({
    queryKey: ['drawPlayResult', userId, drawPlayId],
    enabled: !!drawPlayId,
    queryFn: async () => {
      const drawResponse = adaptDrawResponse(await getDrawPlayResult(userId, drawPlayId));
      if (!drawResponse.isPending) {
        onSuccess?.();
      }
      return drawResponse;
    },
    // Keep polling until the result is not pending
    refetchInterval: ({ state: { data } }) => {
      return data?.isPending ? 1000 : false;
    }
  });

  return {
    amountWon: data?.pointsWon,
    isPending: data?.isPending,
    isLoading,
    error
  };
};
