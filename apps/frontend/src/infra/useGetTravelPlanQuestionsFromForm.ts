import { useQuery } from '@tanstack/react-query';
import { baseUrl } from './constants';
import { z } from 'zod';
import { GameResponse } from './GameState.type';

export const GET_TRAVEL_PLAN_QUESTIONS_FOR_FORM_QUERY_KEY = 'travelPlanQuestionsForForm';

const travelPlanQuestionSchema = z.object({
  question: z.string(),
  questionFormat: z.enum(["Text Input", "Date Picker Range", "Date Select Day", "Combobox"]),
  questionPlaceholderText: z.string().nullable(),
  questionId: z.string(),
  comboboxOptions: z.array(z.string()).optional(),
});

const travelPlanQuestionsSchema = z.object({
  formTitle: z.string().nullable(),
  questions: z.array(travelPlanQuestionSchema),
});


export type TravelPlanQuestion = z.infer<typeof travelPlanQuestionSchema>;
export type TravelPlanQuestions = z.infer<typeof travelPlanQuestionsSchema>;

const fetchTravelPlanQuestionsForForm = async (formId?: string) => {
  if (!formId) {
    throw new Error('Form ID is required');
  }
  const url = `${baseUrl}/getTravelPlanQuestionsForForm`;
  const response = await fetch(`${url}?formId=${formId}`, {
    method: 'GET'
  });

  if (response.status === 404) {
    throw new Error('Form not found');
  }

  if (!response.ok) {
    throw new Error('Failed to fetch travel plan questions for form');
  }

  const data = await response.json();
  const travelPlanQuestions = travelPlanQuestionsSchema.parse(data);

  return travelPlanQuestions;
};

type Params = {
  formId: GameResponse['formId'];
};

export const useGetTravelPlanQuestionsForForm = ({ formId }: Params) => {
  const { data, isLoading, error } = useQuery({ queryKey: [GET_TRAVEL_PLAN_QUESTIONS_FOR_FORM_QUERY_KEY, formId], queryFn: async () => fetchTravelPlanQuestionsForForm(formId), enabled: !!formId });

  return { travelPlanQuestions: data, isLoading, error };
};
