import policyInactiveImage from '../../assets/policy-inactive.webp';
import { ArrowRightIcon } from '../../components/icons/ArrowRightIcon';
import { HeroTemplate } from '../../components/HeroTemplate';

export const WandaPolicyInactive = () => (
  <HeroTemplate
    image={policyInactiveImage}
    title={'Your Wanda subscription has expired!'}
    description={'Want to play the Prize Draw?'}
    isTitleSmaller>
    <a
      href="https://wanda.world/subscription"
      className={
        'group bg-wanda-black hover:bg-wanda-white hover:text-wanda-black disabled:bg-wanda-white disabled:text-wanda-black disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-4 rounded-lg flex flex-row items-center gap-2 cursor-pointer transition-colors duration-300'
      }>
      <span>Resubscribe</span>
      <ArrowRightIcon size={16} className="group-hover:stroke-wanda-black stroke-wanda-white" />
    </a>
  </HeroTemplate>
);
