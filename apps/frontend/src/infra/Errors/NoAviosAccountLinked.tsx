import heroImageDesktopWithoutBadge from '../../assets/hero-image-desktop-without-badge.webp';
import { ArrowRightIcon } from '../../components/icons/ArrowRightIcon';
import { HeroTemplate } from '../../components/HeroTemplate';

export const NoAviosAccountLinked = () => (
  <HeroTemplate
    image={heroImageDesktopWithoutBadge}
    title={'Link your Avios!'}
    description={'Before you can play, we need to know where to send any prizes to.'}
    children={
      <a
        href="https://insurance.wanda.world/login?partner=412636cc-73bd-4c74-9cc6-90db0f9ebfab&redirect=https:%2F%2Fwww.wanda.world%2F"
        className={
          'group bg-wanda-black hover:bg-wanda-white hover:text-wanda-black disabled:bg-wanda-white disabled:text-wanda-black disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-4 rounded-lg flex flex-row items-center gap-2 cursor-pointer transition-colors duration-300'
        }>
        <span>Link your Avios</span>
        <ArrowRightIcon size={16} className="group-hover:stroke-wanda-black stroke-wanda-white" />
      </a>
    }
  />
);
