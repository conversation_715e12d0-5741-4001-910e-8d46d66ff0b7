import { colors } from './src/constants/colors';

/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Schibsted Grotesk', 'sans-serif']
      },
      breakpoints: {
        '2xl': '1400px'
      },
      colors: {
        'wanda-blue': '#3b4ced',
        'wanda-blue-dark': '#062163',
        'wanda-cream': '#f5f1ed',
        'wanda-white': '#fefefd',
        'wanda-black': '#252323',
        'wanda-black-cta': '#252323',
        'wanda-dark-grey': '#212020',
        'paddle-half-cream': '#f5f1ed80',
        'paddle-semi-skimmed': '#f5f1edbf',
        'paddle-orange': '#febd29',
        'surface-light': colors.surfaceLight,
        'border-primary': colors.borderPrimary,
        'text-darkest': colors.textDarkest,
        'text-grey': colors.textGrey
      },
      keyframes: {
        slide: {
          '0%': { left: '0' },
          '100%': { left: '-100%' }
        },
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-2312px)' } // Width of the 4 luggage images
        }
      },
      animation: {
        slide_slow: 'slide 20s linear infinite',
        slide_fast: 'slide 5s linear infinite',
        scrolling_slow: 'scroll 50s linear infinite',
        scrolling_fast: 'scroll 1s linear infinite'
      }
    }
  },
  plugins: []
};
