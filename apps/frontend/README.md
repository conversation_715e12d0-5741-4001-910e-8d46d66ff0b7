# Frontend Setup Guide

The frontend application is built using Vite. This guide will help you get started with the development environment.

## Prerequisites

- Node.js (v18 or higher)
- pnpm (`npm install -g pnpm`)

## Initial Setup

1. Clone the repository
2. Install dependencies:

```bash
pnpm install
```

## Environment Variables

Create a `.env` file in the frontend directory - see `.env.example` for reference (its the staging backend endpoint).

## Local Development

1. Start the development server:

```bash
pnpm dev
```
