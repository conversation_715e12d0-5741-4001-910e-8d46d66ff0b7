{"name": "wanda-prizedraw-fe", "version": "0.0.0", "private": true, "type": "module", "scripts": {"typecheck": "tsc --noEmit", "dev": "vite --port=3001", "build": "vite build", "serve": "vite preview", "start": "vite", "lint": "eslint src/**/*.ts src/**/*.tsx", "lint:fix": "eslint --fix", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "test": "vitest"}, "devDependencies": {"@eslint/js": "^9.17.0", "@hookform/devtools": "^4.3.3", "@tanstack/eslint-plugin-query": "^5.62.9", "@tanstack/router-plugin": "^1.93.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "globals": "^15.14.0", "happy-dom": "^17.0.0", "jest": "^29.7.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript-eslint": "^8.19.0", "vite": "^6.0.3", "vitest": "^3.0.5"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-devtools": "^5.65.1", "@tanstack/react-router": "^1.93.0", "@tanstack/router-devtools": "^1.93.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "react": "^18.3.1", "react-confetti-explosion": "^2.1.2", "react-day-picker": "^9.5.0", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.2", "zod": "^3.24.1"}}