# User Synchronization

The system maintains user data synchronization between multiple services through scheduled AWS Lambda functions. This document describes the synchronization flow and components involved.

## Eligibility Criteria

Users are considered eligible to play the prize draw if they have the following:

- A valid BAEC membership ID
- They have the contact "prize_draw_sync" property set to true
- They have an associated policy with:
  - A status of "Active"
  - A policy type of "Wanda Subscription"

## Sync Process

### New User Sync (10-minute interval)

A scheduled Lambda function runs every 10 minutes to:

1. Query HubSpot for users created within the last 10 minutes
2. Process and add these users to DynamoDB

### User Updates Sync (10-minute interval)

A separate scheduled Lambda function runs every 10 minutes to:

1. Query HubSpot for users updated within the last 10 minutes (excluding creation time updates)
2. Update existing user records in DynamoDB
3. Apply any policy or business account executive club changes

## Flow Diagram

For a visual representation of this flow, see the [user sync flow diagram](./assets/user-sync-flow.svg).
