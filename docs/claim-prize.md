# Prize Claiming Process

The prize claiming system uses a queued approach to ensure fair and orderly prize distribution. This document outlines the sequence of events when a user claims a prize.

## Flow Overview

1. User initiates claim:

   - User clicks "spin" on the website
   - Website starts a 6-second animation sequence
   - During animation, API Gateway is called

2. Queue Processing:

   - Messages are sent to SQS with batch size of 1
   - This ensures only one prize claim is processed at a time
   - SQS triggers the `DrawPrizeLambda` function

3. Prize Assignment:
   - Lambda queries first available slot where `is_claimed = false`
   - Uses DynamoDB transaction to:
     - Mark slot as claimed
     - Create new prize draw record with user ID

## Frontend Polling

Since prize claiming is asynchronous:

- Frontend polls the `pollForPrize` endpoint
- Continues until prize result is available for the user
- Result is displayed to user after animation completes

## User Restrictions

To maintain fair play, the system implements two key restrictions:

1. **Play Frequency Lock**:

   - DynamoDB item lock prevents same user from playing in quick succession

2. **Monthly Play Limit**:
   - Users are limited to two plays per prize draw

For a visual representation of this flow, see the [prize claim flow diagram](./assets/claim-prize-flow.svg).
