# Prize Allocation System

The prize allocation system follows a "predetermined raffle" model where prize slots are generated and assigned prizes before users play. This ensures fair and predictable prize distribution while maintaining excitement through randomization.

## Slot Generation

1. The system generates slots based on the formula:

   ```
   total_slots = (active_users * 2) + buffer
   ```

   where the buffer is configurable in Hubspot.

2. These slots are stored in the `PrizeSlotsTable` in DynamoDB (distinct from the `PrizeDrawTable` used for other prize-related data).

## Prize Assignment Process

When a prize draw starts (not at creation time):

1. Prizes configured in Hubspot are randomly assigned to the generated slots
2. Each slot receives a priority level:
   - Normal slots: priority = 1
   - Guaranteed prize slots: priority = 0 (ensuring they're claimed first)

### Guaranteed Prize Handling

For guaranteed prizes (e.g., 1 million Avios per month):

1. A random datetime is selected within the first 3/4 of the month
2. When this time occurs, the guaranteed prize is placed at the front of the queue by creating a new slot with priority 0
3. The next user to play after this time will automatically win the guaranteed prize due to the priority-based DynamoDB query

See the [prize allocation flow diagram](./assets/prize-allocation.svg).
