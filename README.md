# Prize Draw System Documentation

This documentation covers the various components and flows of the prize draw system.

## Setup Guides

- [Frontend Setup](./apps/frontend/README.md)
- [Backend Setup](./apps/backend/README.md)

## Table of Contents

1. [User Synchronization](./docs/users.md)

   - How users are synced between systems
   - Scheduled Lambda functions
   - Data flow between Wanda Website, HubSpot, and DynamoDB

2. [Prize Allocation](./docs/prize-allocation.md)

   - Predetermined raffle model
   - Slot generation and assignment
   - Guaranteed prize handling
   - Prize slot storage

3. [Prize Claiming](./docs/claim-prize.md)
   - Queue processing
   - Frontend polling system
   - User play restrictions
   - Transaction handling

## System Diagrams

- [Architecture Diagram](./docs/assets/archi-diagram.svg)
- [User Sync Flow](./docs/assets/user-sync-flow.svg)
- [Prize Allocation Flow](./docs/assets/prize-allocation.svg)
- [Prize Claim Flow](./docs/assets/claim-prize-flow.svg)

## Domain model generation
To generate the domain model png when modifying `domain-info.yaml`, run

   ```bash
   npx declagram domain-info.yaml
   ```
