# yaml-language-server: $schema=https://domain-model-schema.s3.eu-west-2.amazonaws.com/domain-model.json
domains:
  Prizedraw:
    roles:
      - Online Customer
      - Ventures Marketer
    journeys:
      Configure monthly Prizedraw (A):
        - A.Between 20th and end of month
        - A.Find the monthly campaign
        - A.Create the Prizedraw slots
      Fulfil the Prizedraw Avios Award (B):
        - B.After the nd of the month
        - B.List the Prizedraw winners
        - Investigate compliance to T&Cs
        - Request avios awards by Avios back office
      Participate in monthy Prizedraw (C):
        - C.Prerequisite Wanda Plus policy
        - C.Receive Prizedraw entry email
        - C.Play once
        - C.Submit travel plans
        - C.Play again
        - C.Wait until next month
    entities:
      Customer:
        attributes:
          Hubspot Id:
            sensitivity: pseudoPII
          First Name:
            sensitivity: PII
          Last Name:
            sensitivity: PII
          Email:
            sensitivity: PII
          BA Club Number:
            sensitivity: pseudoPII
          Insurance Policy Ids:
            sensitivity: pseudoPII
          eSIM Subscription Ids:
            sensitivity: pseudoPII
          Wanda Plus Subscriber:
          Wanda Marketing Consent:
          Roam Marketing Consent:
          Prizedraw CUID:
          Avios Award Ids:
        verbs:
          Purchase Wanda Plus policy:
          Receive Prizedraw email:
          Participate in Prizedraw:
          Submit travel plans:
          Receive Avios Award:
        events:
          - Has CUID allocated
          - Received Prizedraw email
          - Played Once
          - Submitted Travel Plans
          - Played Twice
          - Received Avios Award
      Prize Configurations:
        attributes: 
          Name:
          Prize Amount:
          Maximum Number of Wins:
          Prize Draw Ids:
        verbs:
          Add To Prizedraw:
        events:
          - Added
      Prizedraw Plays:
        attributes: 
          Play Reference UUID:
          Play Name:
          Play Outcome:
          Prizedraw Name:
          Contact Id:
            sensitivity: pseudoPII
          Prizedraw Id:
        verbs:
          Track play result:
        events:
          - Tracked
      Prize Draws:
        attributes: 
          Prizedraw Reference UUID:
          Prizedraw Name:
          Prize Slot Buffer:
          Start date:
          End date:
          Prize Configuration Ids:
          Prizedraw Play Ids:
          Travel Plan Submission Ids:
        verbs:
          Create prize Draws:
          Activate on Start Date:
          Deactivate on End Date:
          Attach prize Configurations:
          Attach Prizedraw Plays:
          Attach travel Plan Submissions:
        events:
          - Active
          - Deactivated
      Travel Plan Submissions:
        attributes:
          CUID:
          Answer:
            sensitivity: PII
          Contact Id:
            sensitivity: pseudoPII
          Prizedraw Id:
          Travel Plan Form Id:
          Travel Plan Question Id:
          Travel Plan Combobox Option Id:
        verbs:
          Submit Travel Plan:
        events:
          - Submitted
      Travel Plan Forms:
        attributes:
          CUID:
          Form Name:
          Title:
          Weighted Probability:
          Travel Plan Submission Ids:
          Travel Plan Question Ids:
          Travel Plan Combobox Option Ids:
        verbs:
          Create Travel Plan Form:
        events:
          - Created
      Travel Plan Questions:
        attributes:
          CUID:
          Question Name:
          Question Text:
          Question Format:
          Question Placeholder Text:
          Travel Plan Submission Ids:
          Travel Plan Form Ids:
          Travel Plan Combobox Option Ids:
        verbs:
          Create Travel Plan Question:
        events:
          - Created
      Travel Plan Combobox Options:
        attributes:
          CUID:
          Option Name:
          Option Text:
          Travel Plan Submission Ids:
          Travel Plan Form Ids:
          Travel Plan Question Ids:
        verbs:
          Create Travel Plan Combobox Option:
        events:
          - Created
      Avios Award:
        attributes:
          Id:
          Amount:
          Date:
          Description:
          Reason:
        verbs:
          Award to Customer:
        events:
          - Awarded