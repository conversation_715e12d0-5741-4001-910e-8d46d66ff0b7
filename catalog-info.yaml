apiVersion: avios.com/v1alpha1
kind: Component
metadata:
  name: prize-draw-service
  namespace: loyalty
  description: Contains prizedraw functionalities, allowing BAEC members to participate in prize draws as Wanda customers.
  annotations:
    snyk.io/org-name: loyalty-ventures-prizedraw
    snyk.io/project-ids:  ************************************
    sonarqube.org/project-key: iagl-loyalty_ventures-prizedraw-for-wanda
  tags:
    ["typescript","node", "cdk"]
  links:
    - url:  https://aviosgroup.atlassian.net/wiki/spaces/IVT/pages/4104388728/Wanda+Prize+Draw+testing
      title: Confluence
    - url: https://github.com/iagl-loyalty/ventures-prizedraw-for-wanda
      title: Github Repo
spec:
  type: Service
  lifecycle: production
  owner: group:loyalty/prize-draw-team
  product: product:loyalty/prize-draw
  profile:
    displayName: prize-draw-service
  providesApis:  [wanda-prize-draw-api]

---

apiVersion: avios.com/v1alpha1
kind: API
metadata:
  name: wanda-prize-draw-api
  namespace: loyalty
  description: A high availability API built to (1) retrieve user prizedraw status (2) retrieve the result a one prize draw for a user (3) retrieve the travel plans form (4) submit the travel plans form (5) initialise a prizedraw play.
  tags:
    ["typescript","node", "cdk"]
  links:
    - url: https://aviosgroup.atlassian.net/wiki/spaces/IVT/pages/4104388728/Wanda+Prize+Draw+testing
      title: Confluence
    - url: https://github.com/iagl-loyalty/ventures-prizedraw-for-wanda
      title: Github Repo
spec:
  type: Service
  lifecycle: production
  owner: group:loyalty/prize-draw-team
  product: product:loyalty/prize-draw
  profile:
      displayName: wanda-prize-draw-api
  providesApis: [wanda-prize-draw-api]