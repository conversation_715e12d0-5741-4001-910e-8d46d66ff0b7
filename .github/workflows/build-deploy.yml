name: Build and Deploy

on:
  workflow_call:
    inputs:
      env:
        required: true
        type: string
    secrets:
      HUBSPOT_API_TOKEN_SECRET_NAME:
        required: true
      AWS_ACCOUNT_ID:
        required: true

jobs:
  build-deploy:
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest
    environment: ${{ inputs.env }}
    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      BUCKET_NAME: ${{ vars.BUCKET_NAME }}
      CLOUDFRONT_DISTRIBUTION_ID: ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }}
      BACKEND_BASE_URL: ${{ vars.BACKEND_BASE_URL }}
      ALARM_EMAILS: ${{ vars.ALARM_EMAILS }}
    steps:
      - name: Variables
        run: |
          echo "HubSpot API Token: ${{ secrets.HUBSPOT_API_TOKEN_SECRET_NAME }}"
          echo "AWS Account ID: ${{ secrets.AWS_ACCOUNT_ID }}"
          echo "AWS Region: ${{ env.AWS_REGION }}"
          echo "Bucket name: ${{ env.BUCKET_NAME }}"
          echo "CloudFront distribution id: ${{ env.CLOUDFRONT_DISTRIBUTION_ID }}"
          echo "Backend base URL: ${{ env.BACKEND_BASE_URL }}"
          echo "Alarm email list: ${{ env.ALARM_EMAILS }}"

      - name: Git clone the repository
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "pnpm"

      - name: 📦 Install Dependencies
        run: |
          pnpm install

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID }}:role/ventures-prizedraw-github-actions-deploy-role
          aws-region: ${{ env.AWS_REGION }}

      - name: 🏗️ Build the frontend
        env:
          VITE_BACKEND_BASE_URL: ${{ env.BACKEND_BASE_URL }}
        run: |
          cd apps/frontend
          pnpm build

      - name: 🧐 Check for backend changes
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            backend:
              - 'apps/backend/**'

      - name: 🚀 Deploy Infrastructure & Backend
        if: steps.filter.outputs.backend == 'true'
        env:
          STAGE: staging
        run: |
          cd apps/backend
          HUBSPOT_API_TOKEN_SECRET_NAME=${{ secrets.HUBSPOT_API_TOKEN_SECRET_NAME }} pnpm run deploy -c stage=$STAGE

      - name: 📤 Copy frontend to S3
        run: |
          aws s3 cp apps/frontend/dist s3://${{ env.BUCKET_NAME }}/ --recursive

      - name: 🔄 Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

