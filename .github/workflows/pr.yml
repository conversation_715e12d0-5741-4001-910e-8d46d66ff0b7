name: Check PR
on: pull_request
env:
  BUCKET_NAME: "ventures-wanda-prizedraw"
  AWS_REGION: ${{ vars.AWS_REGION }}
  AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
  CLOUDFRONT_DISTRIBUTION_ID: "E10DUBAYN94NMJ"
permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout
jobs:
  CheckPR:
    runs-on: ubuntu-latest
    steps:
      - name: Git clone the repository
        uses: actions/checkout@v4

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/ventures-prizedraw-github-actions-deploy-role
          aws-region: eu-west-2

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "pnpm"

      - name: 📦 Install Dependencies
        run: |
          pnpm install

      - name: 🏗️ Build the frontend
        run: |
          cd apps/frontend
          pnpm build

      - name: 📋 Plan Deployment
        run: |
          cd apps/backend
          pnpm run pr-synth -c stage=prod
          pnpm run pr-diff -c stage=prod
