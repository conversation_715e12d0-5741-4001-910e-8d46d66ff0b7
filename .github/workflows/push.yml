name: Update Wanda Prize Draw

on:
  push:
    branches:
      - main
jobs:
  main-push:
    runs-on: ubuntu-latest
    steps:
      - name: Git clone the repository
        uses: actions/checkout@v4

  build-deploy-dev:
    uses: ./.github/workflows/build-deploy.yml
    with:
      env: dev
    secrets:
      HUBSPOT_API_TOKEN_SECRET_NAME: ${{ secrets.HUBSPOT_API_TOKEN_SECRET_NAME }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
    needs: main-push

  build-deploy-tst:
    uses: ./.github/workflows/build-deploy.yml
    with:
      env: tst
    secrets:
      HUBSPOT_API_TOKEN_SECRET_NAME: ${{ secrets.HUBSPOT_API_TOKEN_SECRET_NAME }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
    needs: main-push

  build-deploy-stg:
    uses: ./.github/workflows/build-deploy.yml
    with:
      env: stg
    secrets:
      HUBSPOT_API_TOKEN_SECRET_NAME: ${{ secrets.HUBSPOT_API_TOKEN_SECRET_NAME }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
    needs: main-push

