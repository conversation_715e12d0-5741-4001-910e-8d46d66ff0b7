name: Update Wanda Prize Draw
on:
  push:
    branches:
      - production
env:
  BUCKET_NAME: "ventures-wanda-prizedraw"
  AWS_REGION: "eu-west-2"
  PROD_CLOUDFRONT_DISTRIBUTION_ID: "E3UUQG0ZF6K2MA"
  HUBSPOT_API_TOKEN_SECRET_NAME: ${{ secrets.HUBSPOT_API_TOKEN_SECRET_NAME_PROD }}
  BACKEND_BASE_URL: "https://osq20wx195.execute-api.eu-west-2.amazonaws.com/prod"
  AWS_ACCOUNT_ID: "************"
  PAGERDUTY_ROUTING_KEY: "ca7a2d11c1044304c02cf39367074591"
  ALARM_EMAILS: "<EMAIL>"
permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout
jobs:
  UpdateApp:
    runs-on: ubuntu-latest
    steps:
      - name: Git clone the repository
        uses: actions/checkout@v4

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/ventures-prizedraw-github-actions-deploy-role
          aws-region: ${{ env.AWS_REGION }}

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "pnpm"
      - name: 📦 Install Dependencies
        run: |
          pnpm install

      - name: 🏗️ Build the frontend
        env:
          VITE_BACKEND_BASE_URL: ${{ env.BACKEND_BASE_URL }}
        run: |
          cd apps/frontend
          pnpm build

      - name: 🚀 Deploy Infrastructure & Backend
        env:
          STAGE: prod
        run: |
          cd apps/backend
          HUBSPOT_API_TOKEN_SECRET_NAME=${{ env.HUBSPOT_API_TOKEN_SECRET_NAME }} pnpm run deploy -c stage=$STAGE

      - name: 📤 Copy frontend to S3
        run: |
          aws s3 cp apps/frontend/dist s3://${{ env.BUCKET_NAME }}/ --recursive

      - name: 🔄 Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ env.PROD_CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

      - name: Run Pagerduty Event Action
        uses: iagl-innersource/tooling-centralactions-compliance/.github/actions/pagerduty-change@latest
        with:
          PAGERDUTY_ROUTING_KEY: ${{ secrets.PAGERDUTY_ROUTING_KEY }}
